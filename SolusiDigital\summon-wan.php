<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Include configuration
require_once 'config.php';

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['deviceId'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Device ID is required'
    ]);
    exit;
}

$deviceId = $input['deviceId'];

try {
    error_log("SUMMON WAN: Starting WAN summon for device: $deviceId");

    // Step 1: Get device detail to find WAN paths
    $deviceDetail = getDeviceDetailData($deviceId);
    if (!$deviceDetail) {
        throw new Exception("Failed to get device detail data");
    }

    error_log("SUMMON WAN: Device detail retrieved successfully");

    // Step 2: Extract WAN connection paths
    $wanPaths = extractWANPaths($deviceDetail);
    if (empty($wanPaths)) {
        throw new Exception("No WAN connection paths found");
    }

    error_log("SUMMON WAN: Found " . count($wanPaths) . " WAN paths");

    // Step 3: Batch summon all WAN parameters at once (faster!)
    $parametersToSummon = [];
    foreach ($wanPaths as $path) {
        $parametersToSummon[] = $path . '.WANPPPConnectionNumberOfEntries';
        $parametersToSummon[] = $path . '.WANIPConnectionNumberOfEntries';
    }

    error_log("SUMMON WAN: Batch summoning " . count($parametersToSummon) . " parameters");
    $batchResult = batchSummonParameters($deviceId, $parametersToSummon);

    // Step 4: No need to refresh device - batch summon is enough
    
    error_log("SUMMON WAN: Summon completed successfully");

    echo json_encode([
        'success' => true,
        'message' => 'WAN connections summoned successfully',
        'parameters_summoned' => count($parametersToSummon),
        'batch_result' => $batchResult
    ]);

} catch (Exception $e) {
    error_log("SUMMON WAN: Error - " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Function to get device detail data (using same method as getdetaildevice.php)
function getDeviceDetailData($deviceId) {
    error_log("SUMMON WAN: Getting device detail data for: $deviceId");

    // Use query format like getdetaildevice.php
    $query = json_encode(['_id' => $deviceId]);
    $apiUrl = GENIEACS_URL . '?query=' . urlencode($query);

    error_log("SUMMON WAN: API URL: $apiUrl");

    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_ENCODING, 'gzip');

    $deviceData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("SUMMON WAN: cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    if ($httpCode !== 200) {
        error_log("SUMMON WAN: HTTP error: $httpCode");
        return false;
    }

    $data = json_decode($deviceData, true);
    if (!is_array($data) || empty($data)) {
        error_log("SUMMON WAN: No device data found or invalid response");
        return false;
    }

    error_log("SUMMON WAN: Successfully retrieved device data");
    return $data[0]; // Return first device
}

// Function to extract WAN connection paths from device data
function extractWANPaths($deviceDetail) {
    $wanPaths = [];

    if (!$deviceDetail || !is_array($deviceDetail)) {
        error_log("SUMMON WAN: Invalid device detail data");
        return $wanPaths;
    }

    error_log("SUMMON WAN: Extracting WAN paths from device data");

    // Look for WANConnectionDevice paths in the device data structure
    // Device data comes as flat array with parameter paths as keys
    foreach ($deviceDetail as $key => $value) {
        if (is_string($key)) {
            // Match WANConnectionDevice paths
            if (preg_match('/^InternetGatewayDevice\.WANDevice\.1\.WANConnectionDevice\.(\d+)\./', $key, $matches)) {
                $wanPath = 'InternetGatewayDevice.WANDevice.1.WANConnectionDevice.' . $matches[1];
                if (!in_array($wanPath, $wanPaths)) {
                    $wanPaths[] = $wanPath;
                    error_log("SUMMON WAN: Found WAN path: $wanPath");
                }
            }
        }
    }

    // If no paths found, try to create default paths (1-4)
    if (empty($wanPaths)) {
        error_log("SUMMON WAN: No WAN paths found in data, using default paths");
        for ($i = 1; $i <= 4; $i++) {
            $wanPaths[] = "InternetGatewayDevice.WANDevice.1.WANConnectionDevice.$i";
        }
    }

    // Remove duplicates and sort
    $wanPaths = array_unique($wanPaths);
    sort($wanPaths);

    error_log("SUMMON WAN: Final WAN paths: " . implode(', ', $wanPaths));
    return $wanPaths;
}

// Function to batch summon multiple parameters (faster)
function batchSummonParameters($deviceId, $parameterArray) {
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?connection_request";

    $task = [
        'name' => 'getParameterValues',
        'parameterNames' => $parameterArray
    ];

    error_log("SUMMON WAN: Batch summoning parameters: " . implode(', ', $parameterArray));

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 8); // Faster timeout like summon-detaildevice

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("SUMMON WAN: Batch cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    $success = ($httpCode === 200 || $httpCode === 201 || $httpCode === 202);
    error_log("SUMMON WAN: Batch summon result: " . ($success ? 'success' : 'failed') . " (HTTP: $httpCode)");

    return $success;
}

// Function to summon a specific parameter (legacy - not used anymore)
function summonParameter($deviceId, $parameterPath) {
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000&connection_request";

    $task = [
        'name' => 'getParameterValues',
        'parameterNames' => [$parameterPath]
    ];

    error_log("SUMMON WAN: Summoning parameter: $parameterPath");

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("SUMMON WAN: cURL error for $parameterPath: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    $success = ($httpCode === 200 || $httpCode === 202);
    error_log("SUMMON WAN: Parameter $parameterPath summon result: " . ($success ? 'success' : 'failed') . " (HTTP: $httpCode)");

    return $success;
}

// Note: refreshDevice function removed - not needed with batch summon
?>
