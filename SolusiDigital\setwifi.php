<?php
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$deviceId = $input['deviceId'] ?? null;
$path = $input['path'] ?? null;
$value = $input['value'] ?? null;
$dataType = $input['dataType'] ?? 'xsd:string';

if (!$deviceId) {
    http_response_code(400);
    echo json_encode(['error' => 'Device ID is required']);
    exit;
}

if (!$path) {
    http_response_code(400);
    echo json_encode(['error' => 'Parameter path is required']);
    exit;
}

if ($value === null || $value === '') {
    http_response_code(400);
    echo json_encode(['error' => 'Parameter value is required']);
    exit;
}

try {
    // Validate path format
    if (!preg_match('/^InternetGatewayDevice\.LANDevice\.1\.WLANConfiguration\.\d+\./', $path)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid parameter path format']);
        exit;
    }

    // Additional validation for password
    if (strpos($path, 'PreSharedKey') !== false && strlen($value) < 8) {
        http_response_code(400);
        echo json_encode(['error' => 'Password must be at least 8 characters long']);
        exit;
    }

    // Prepare parameter values array
    $parameterValues = [
        [$path, $value, $dataType]
    ];
    
    // Prepare GenieACS task
    $task = [
        'name' => 'setParameterValues',
        'parameterValues' => $parameterValues
    ];
    
    // Send to GenieACS
    $result = sendGenieACSTask($deviceId, $task);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => "Parameter updated successfully",
            'path' => $path,
            'value' => strpos($path, 'PreSharedKey') !== false ? '[HIDDEN]' : $value,
            'dataType' => $dataType
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'error' => 'Failed to update parameter',
            'path' => $path,
            'details' => $result['error']
        ]);
    }
    
} catch (Exception $e) {
    error_log("Set WiFi Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}

// Function to send task to GenieACS
function sendGenieACSTask($deviceId, $task) {
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000";
    
    error_log("Sending WiFi task to GenieACS: " . json_encode($task));
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    if ($curlError) {
        error_log("WiFi cURL error: $curlError");
        return ['success' => false, 'error' => "Connection error: $curlError"];
    }
    
    if ($httpCode === 200 || $httpCode === 202) {
        error_log("WiFi task successful - HTTP $httpCode");
        return ['success' => true, 'response' => $result];
    }
    
    error_log("WiFi task failed - HTTP $httpCode, Response: $result");
    return ['success' => false, 'error' => "HTTP $httpCode: $result"];
}
?>
