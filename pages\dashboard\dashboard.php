<!-- //dashboard -->
<div class="fade-in">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 rounded-xl">
                    <i class="fas fa-router text-blue-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Devices</p>
                    <p class="text-3xl font-bold text-gray-900" id="total-devices">1,234</p>
                    <p class="text-xs text-green-600 mt-1">
                        <i class="fas fa-arrow-up mr-1"></i>+12 this month
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 rounded-xl">
                    <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Online</p>
                    <p class="text-3xl font-bold text-gray-900" id="online-devices">1,156</p>
                    <p class="text-xs text-green-600 mt-1">
                        <i class="fas fa-arrow-up mr-1"></i>93.7% uptime
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="p-3 bg-red-100 rounded-xl">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Offline</p>
                    <p class="text-3xl font-bold text-gray-900" id="offline-devices">78</p>
                    <p class="text-xs text-red-600 mt-1">
                        <i class="fas fa-arrow-down mr-1"></i>6.3% down
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="p-3 bg-yellow-100 rounded-xl">
                    <i class="fas fa-clock text-yellow-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending Tasks</p>
                    <p class="text-3xl font-bold text-gray-900" id="pending-tasks">23</p>
                    <p class="text-xs text-yellow-600 mt-1">
                        <i class="fas fa-clock mr-1"></i>In queue
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity & Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Activity -->
        <div class="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium" onclick="refreshActivity()">
                    <i class="fas fa-sync mr-1"></i>Refresh
                </button>
            </div>
            <div class="p-6">
                <div id="activity-list" class="space-y-4">
                    <div class="flex items-start p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-4 mt-1 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Device CPE-001234 came online</p>
                            <p class="text-xs text-gray-500 mt-1">Router reconnected successfully • 2 minutes ago</p>
                        </div>
                    </div>
                    <div class="flex items-start p-5 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-5 mt-1 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="text-base font-medium text-gray-900">Firmware update completed</p>
                            <p class="text-sm text-gray-500 mt-1">15 devices updated to v2.1.3 • 5 minutes ago</p>
                        </div>
                    </div>
                    <div class="flex items-start p-5 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                        <div class="w-4 h-4 bg-red-500 rounded-full mr-5 mt-1 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="text-base font-medium text-gray-900">Device CPE-005678 went offline</p>
                            <p class="text-sm text-gray-500 mt-1">Connection timeout detected • 10 minutes ago</p>
                        </div>
                    </div>
                    <div class="flex items-start p-5 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full mr-5 mt-1 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="text-base font-medium text-gray-900">Configuration backup created</p>
                            <p class="text-sm text-gray-500 mt-1">Auto backup for 234 devices • 15 minutes ago</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
            </div>
            <div class="p-6 space-y-3">
                <button onclick="addDevice()" class="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Add Device
                </button>
                <button onclick="backupConfig()" class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Backup Config
                </button>
                <button onclick="refreshAll()" class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-sync mr-2"></i>
                    Refresh All
                </button>
                <button onclick="viewReports()" class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-chart-bar mr-2"></i>
                    View Reports
                </button>
            </div>
        </div>
    </div>
</div>
