<?php
// Prevent PHP warnings from corrupting JSON output
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit;
}

// Function to make API request to GenieACS
function makeGenieRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();

    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 8,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json'
        ]
    ]);

    if ($data && ($method === 'POST' || $method === 'PUT')) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("cURL Error: " . $error);
    }

    return [
        'http_code' => $http_code,
        'response' => $response ? json_decode($response, true) : null
    ];
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['deviceId'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Device ID is required'
    ]);
    exit;
}

$deviceId = $input['deviceId'];

// Setup API URL - GENIEACS_URL sudah include /devices
$api_url = GENIEACS_URL;

// WiFi parameters to summon
$wifi_parameters = [
    // WiFi Configuration 1
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.IEEE11iAuthenticationMode',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.TotalAssociations',

    // WiFi Configuration 2
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.IEEE11iAuthenticationMode',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.TotalAssociations',

    // WiFi Configuration 3
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.IEEE11iAuthenticationMode',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.TotalAssociations',

    // WiFi Configuration 4
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.IEEE11iAuthenticationMode',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.TotalAssociations'
];

// Function to summon parameters via GenieACS API
function summonParameters($deviceId, $parameters) {
    global $baseUrl;

    if (empty($parameters)) {
        return ['success' => false, 'error' => 'No parameters to summon'];
    }

    try {
        // Group parameters by WiFi configuration object for batch processing
        $wifiObjects = [];
        foreach ($parameters as $param) {
            // Extract the base WiFi configuration path
            if (preg_match('/^(InternetGatewayDevice\.LANDevice\.1\.WLANConfiguration\.\d+)\./', $param, $matches)) {
                $baseObject = $matches[1];
                if (!in_array($baseObject, $wifiObjects)) {
                    $wifiObjects[] = $baseObject;
                }
            }
        }

        // Create summon requests for each WiFi configuration object
        $tasks = [];
        foreach ($wifiObjects as $wifiObject) {
            $tasks[] = [
                'name' => 'refreshObject',
                'objectName' => $wifiObject
            ];
        }

        // Send requests to GenieACS
        $successCount = 0;
        $errors = [];

        foreach ($tasks as $task) {
            // Use the correct GenieACS tasks endpoint
            $url = str_replace('/devices', '', $baseUrl) . "/tasks?connection_request";

            $postData = json_encode([
                'device' => $deviceId,
                'name' => $task['name'],
                'objectName' => $task['objectName']
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200 || $httpCode === 202) {
                $successCount++;
            } else {
                $errors[] = "Failed to summon " . $task['objectName'] . " (HTTP " . $httpCode . ")";
            }

            // Small delay between requests
            usleep(100000); // 0.1 second
        }

        if ($successCount > 0) {
            return [
                'success' => true,
                'summoned_count' => $successCount,
                'total_objects' => count($wifiObjects),
                'errors' => $errors
            ];
        } else {
            return ['success' => false, 'error' => 'All summon requests failed: ' . implode(', ', $errors)];
        }

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Summon error: ' . $e->getMessage()];
    }
}

// Main execution
try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['deviceId'])) {
        echo json_encode(['success' => false, 'error' => 'Device ID is required']);
        exit;
    }
    
    $deviceId = $input['deviceId'];
    
    // Get device data
    $deviceData = getDeviceDetailData($deviceId);
    if (!$deviceData) {
        echo json_encode(['success' => false, 'error' => 'Device not found or no data available']);
        exit;
    }
    
    // Extract WiFi paths
    $wifiPaths = extractWiFiPaths($deviceData);
    
    if (empty($wifiPaths)) {
        echo json_encode(['success' => false, 'error' => 'No WiFi parameters found for this device']);
        exit;
    }
    
    // Summon the parameters
    $result = summonParameters($deviceId, $wifiPaths);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'WiFi SSIDs summoned successfully',
            'summoned_count' => $result['summoned_count'],
            'total_objects' => $result['total_objects'],
            'parameters_found' => count($wifiPaths),
            'errors' => $result['errors'] ?? []
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => $result['error']]);
    }
    
} catch (Exception $e) {
    error_log("Summon WiFi Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>
