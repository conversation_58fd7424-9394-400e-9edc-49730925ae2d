<?php
// Prevent PHP warnings from corrupting JSON output
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit;
}

// Function to make API request to GenieACS
function makeGenieRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();

    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 8,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json'
        ]
    ]);

    if ($data && ($method === 'POST' || $method === 'PUT')) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("cURL Error: " . $error);
    }

    return [
        'http_code' => $http_code,
        'response' => $response ? json_decode($response, true) : null
    ];
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['deviceId'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Device ID is required'
    ]);
    exit;
}

$deviceId = $input['deviceId'];

// Setup API URL - GENIEACS_URL sudah include /devices
$api_url = GENIEACS_URL;

// WiFi parameters to summon
$wifi_parameters = [
    // WiFi Configuration 1
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.TotalAssociations',
    // Password parameters for Huawei and ZTE
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.PreSharedKey.1.KeyPassphrase', // Huawei
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.KeyPassphrase', // ZTE

    // WiFi Configuration 2
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.TotalAssociations',
    // Password parameters for Huawei and ZTE
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.PreSharedKey.1.KeyPassphrase', // Huawei
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.KeyPassphrase', // ZTE

    // WiFi Configuration 3
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.TotalAssociations',
    // Password parameters for Huawei and ZTE
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.PreSharedKey.1.KeyPassphrase', // Huawei
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.KeyPassphrase', // ZTE

    // WiFi Configuration 4
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.TotalAssociations',
    // Password parameters for Huawei and ZTE
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.PreSharedKey.1.KeyPassphrase', // Huawei
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.KeyPassphrase' // ZTE
];

try {
    // Use the correct GenieACS device-specific tasks endpoint
    $device_tasks_url = $api_url . '/' . urlencode($deviceId) . '/tasks?connection_request';

    $summon_count = 0;
    $failed_count = 0;

    // Batch summon WiFi parameters
    try {
        $task_data = [
            'name' => 'getParameterValues',
            'parameterNames' => $wifi_parameters
        ];

        $task_response = makeGenieRequest($device_tasks_url, 'POST', $task_data);

        if ($task_response['http_code'] === 200 || $task_response['http_code'] === 201 || $task_response['http_code'] === 202) {
            $summon_count += count($wifi_parameters);
        } else {
            $failed_count += count($wifi_parameters);
            error_log("Failed to summon WiFi parameters, HTTP Code: " . $task_response['http_code']);
        }
    } catch (Exception $e) {
        $failed_count += count($wifi_parameters);
        error_log("Exception while summoning WiFi parameters: " . $e->getMessage());
    }

    // Prepare response based on results
    if ($summon_count > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'WiFi SSIDs summoned successfully',
            'summoned_count' => $summon_count,
            'total_parameters' => count($wifi_parameters)
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to summon WiFi parameters'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Error summoning WiFi parameters: ' . $e->getMessage()
    ]);
    error_log("Summon WiFi Error: " . $e->getMessage());
}
?>
