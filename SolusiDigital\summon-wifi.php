<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include database connection
require_once 'config.php';

// Function to get device detail data
function getDeviceDetailData($deviceId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM devices WHERE _id = ?");
        $stmt->execute([$deviceId]);
        $device = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$device) {
            return null;
        }
        
        // Parse the device data
        $deviceData = json_decode($device['data'], true);
        return $deviceData;
        
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return null;
    }
}

// Function to extract WiFi paths from device data
function extractWiFiPaths($deviceData) {
    $wifiPaths = [];
    
    if (!$deviceData) {
        return $wifiPaths;
    }
    
    // Look for WiFi SSID parameters in device data
    foreach ($deviceData as $key => $value) {
        // Check for WiFi SSID paths
        if (strpos($key, 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.') !== false && 
            strpos($key, '.SSID') !== false) {
            $wifiPaths[] = $key;
        }
        
        // Also check for Enable parameters
        if (strpos($key, 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.') !== false && 
            strpos($key, '.Enable') !== false) {
            $wifiPaths[] = $key;
        }
        
        // Check for BeaconType (security)
        if (strpos($key, 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.') !== false && 
            strpos($key, '.BeaconType') !== false) {
            $wifiPaths[] = $key;
        }
        
        // Check for AuthenticationServiceMode
        if (strpos($key, 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.') !== false && 
            strpos($key, '.IEEE11iAuthenticationMode') !== false) {
            $wifiPaths[] = $key;
        }
        
        // Check for Channel
        if (strpos($key, 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.') !== false && 
            strpos($key, '.Channel') !== false) {
            $wifiPaths[] = $key;
        }
        
        // Check for TotalAssociations (connected devices)
        if (strpos($key, 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.') !== false && 
            strpos($key, '.TotalAssociations') !== false) {
            $wifiPaths[] = $key;
        }
    }
    
    // Remove duplicates and sort
    $wifiPaths = array_unique($wifiPaths);
    sort($wifiPaths);
    
    return $wifiPaths;
}

// Function to summon parameters via GenieACS API
function summonParameters($deviceId, $parameters) {
    global $genieacs_url, $genieacs_user, $genieacs_pass;

    if (empty($parameters)) {
        return ['success' => false, 'error' => 'No parameters to summon'];
    }

    try {
        // Group parameters by WiFi configuration object for batch processing
        $wifiObjects = [];
        foreach ($parameters as $param) {
            // Extract the base WiFi configuration path
            if (preg_match('/^(InternetGatewayDevice\.LANDevice\.1\.WLANConfiguration\.\d+)\./', $param, $matches)) {
                $baseObject = $matches[1];
                if (!in_array($baseObject, $wifiObjects)) {
                    $wifiObjects[] = $baseObject;
                }
            }
        }

        // Create summon requests for each WiFi configuration object
        $tasks = [];
        foreach ($wifiObjects as $wifiObject) {
            $tasks[] = [
                'name' => 'refreshObject',
                'objectName' => $wifiObject
            ];
        }

        // Send requests to GenieACS
        $successCount = 0;
        $errors = [];

        foreach ($tasks as $task) {
            $url = $genieacs_url . "/devices/" . urlencode($deviceId) . "/tasks?connection_request";

            $postData = json_encode($task);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Basic ' . base64_encode($genieacs_user . ':' . $genieacs_pass)
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200 || $httpCode === 202) {
                $successCount++;
            } else {
                $errors[] = "Failed to summon " . $task['objectName'] . " (HTTP " . $httpCode . ")";
            }

            // Small delay between requests
            usleep(100000); // 0.1 second
        }

        if ($successCount > 0) {
            return [
                'success' => true,
                'summoned_count' => $successCount,
                'total_objects' => count($wifiObjects),
                'errors' => $errors
            ];
        } else {
            return ['success' => false, 'error' => 'All summon requests failed: ' . implode(', ', $errors)];
        }

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Summon error: ' . $e->getMessage()];
    }
}

// Main execution
try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['deviceId'])) {
        echo json_encode(['success' => false, 'error' => 'Device ID is required']);
        exit;
    }
    
    $deviceId = $input['deviceId'];
    
    // Get device data
    $deviceData = getDeviceDetailData($deviceId);
    if (!$deviceData) {
        echo json_encode(['success' => false, 'error' => 'Device not found or no data available']);
        exit;
    }
    
    // Extract WiFi paths
    $wifiPaths = extractWiFiPaths($deviceData);
    
    if (empty($wifiPaths)) {
        echo json_encode(['success' => false, 'error' => 'No WiFi parameters found for this device']);
        exit;
    }
    
    // Summon the parameters
    $result = summonParameters($deviceId, $wifiPaths);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true, 
            'message' => 'WiFi SSIDs summoned successfully',
            'summoned_count' => $result['summoned_count'],
            'parameters' => $wifiPaths
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => $result['error']]);
    }
    
} catch (Exception $e) {
    error_log("Summon WiFi Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>
