<?php
// Get device ID from URL parameter
$device_id = isset($_GET['id']) ? $_GET['id'] : '';
if (empty($device_id)) {
    echo '<div class="text-center py-8"><p class="text-red-600">Device ID not provided</p></div>';
    return;
}
?>

<div class="fade-in">
    <!-- Header with Back Button -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
        <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <button onclick="goBack()" class="inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-all duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                <span class="hidden sm:inline">Back to Devices</span>
                <span class="sm:hidden">Back</span>
            </button>
            <h1 class="text-xl sm:text-2xl font-bold text-gray-900">Device Detail</h1>
        </div>
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            <button id="refresh-device" onclick="refreshDeviceDetail()" class="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-all duration-200">
                <i class="fas fa-sync mr-2"></i>
                Refresh
            </button>
            <button id="summon-device" onclick="summonDeviceDetail()" class="inline-flex items-center justify-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-all duration-200">
                <i class="fas fa-magic mr-2"></i>
                <span class="hidden sm:inline">Summon Device</span>
                <span class="sm:hidden">Summon</span>
            </button>
        </div>
    </div>

    <!-- Device ID Display -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div class="min-w-0 flex-1">
                <h2 class="text-lg font-semibold text-gray-900">Device Information</h2>
                <p class="text-sm text-gray-600 mt-1 break-all">Device ID: <span class="font-mono text-blue-600"><?php echo htmlspecialchars($device_id); ?></span></p>
            </div>
            <div id="device-status" class="flex items-center justify-center sm:justify-end">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <i class="fas fa-spinner fa-spin mr-1.5"></i>
                    Loading...
                </span>
            </div>
        </div>
    </div>

    <!-- Device Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6" id="device-stats">
        <!-- Stats akan dimuat via JavaScript -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-center h-20">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        </div>
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-center h-20">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        </div>
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-center h-20">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        </div>
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-center h-20">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        </div>
    </div>

    <!-- Device Details Tabs -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
            <nav class="flex overflow-x-auto px-4 sm:px-6 scrollbar-hide" aria-label="Tabs">
                <button onclick="switchTab('wan')" class="tab-button active border-transparent text-blue-600 whitespace-nowrap py-4 px-4 sm:px-6 border-b-2 font-medium text-sm flex-shrink-0" data-tab="wan">
                    <i class="fas fa-globe mr-2"></i>
                    WAN
                </button>
                <button onclick="switchTab('wifi')" class="tab-button border-transparent text-gray-500 hover:text-gray-700 whitespace-nowrap py-4 px-4 sm:px-6 border-b-2 font-medium text-sm flex-shrink-0" data-tab="wifi">
                    <i class="fas fa-wifi mr-2"></i>
                    WiFi
                </button>
                <button onclick="switchTab('system')" class="tab-button border-transparent text-gray-500 hover:text-gray-700 whitespace-nowrap py-4 px-4 sm:px-6 border-b-2 font-medium text-sm flex-shrink-0" data-tab="system">
                    <i class="fas fa-cog mr-2"></i>
                    System
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-4 sm:p-6">
            <!-- WAN Tab -->
            <div id="tab-wan" class="tab-content">
                <div id="wan-info">
                    <div class="flex justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                </div>
            </div>

            <!-- WiFi Tab -->
            <div id="tab-wifi" class="tab-content hidden">
                <div class="p-4 sm:p-6">
                    <div id="wifi-info">
                        <div class="flex justify-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Tab -->
            <div id="tab-system" class="tab-content hidden">
                <div class="p-4 sm:p-6">
                    <div id="system-info">
                        <!-- Overview content moved here -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8">
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                                <div id="basic-info" class="space-y-3">
                                    <!-- Basic info akan dimuat via JavaScript -->
                                    <div class="flex justify-center py-8">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900">Connection Status</h3>
                                <div id="connection-info" class="space-y-3">
                                    <!-- Connection info akan dimuat via JavaScript -->
                                    <div class="flex justify-center py-8">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System specific content will be loaded here -->
                        <div class="system-specific-content">
                            <div class="flex justify-center py-8">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden device ID for JavaScript -->
<script>
    window.deviceId = '<?php echo htmlspecialchars($device_id, ENT_QUOTES); ?>';
</script>


<!-- Load device detail JavaScript -->
<script src="pages/device-detail/device-detail.js"></script>
