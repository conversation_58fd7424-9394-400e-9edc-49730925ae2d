<?php
// Prevent PHP warnings from corrupting JSON output
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'alerts' => [['type' => 'error', 'message' => 'Method not allowed']]
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['device_id'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'alerts' => [['type' => 'error', 'message' => 'Device ID is required']]
    ]);
    exit;
}

$device_id = $input['device_id'];
$alerts = [];
$success = true;

// Setup API URL - GENIEACS_URL sudah include /devices
$api_url = GENIEACS_URL;

// Parameters to summon - VirtualParameters dan path level tinggi
$parameters_to_summon = [
    // Virtual Parameters - ini yang paling penting
    'VirtualParameters.pppoeUsername',
    'VirtualParameters.pppoeUsername2',
    'VirtualParameters.RXPower',
    'VirtualParameters.gettemp',
    'VirtualParameters.activedevices',

    
    
    // WiFi Configuration - summon per WLAN level tinggi
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4',

    // Device Info
    'InternetGatewayDevice.DeviceInfo.HardwareVersion',
    'InternetGatewayDevice.DeviceInfo.SoftwareVersion',
    'InternetGatewayDevice.DeviceInfo.UpTime',

    // MAC Address
    // 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress',
    
];

// Function to make API request to GenieACS
function makeGenieRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();

    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 8,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json'
        ]
    ]);

    if ($data && ($method === 'POST' || $method === 'PUT')) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("cURL Error: " . $error);
    }

    return [
        'http_code' => $http_code,
        'response' => $response ? json_decode($response, true) : null
    ];
}

try {
    // Use the correct GenieACS device-specific tasks endpoint
    $device_tasks_url = $api_url . '/' . urlencode($device_id) . '/tasks?connection_request';

    $summon_count = 0;
    $failed_count = 0;

    // Separate VirtualParameters and regular parameters
    $virtual_params = [];
    $regular_params = [];

    foreach ($parameters_to_summon as $parameter) {
        if (strpos($parameter, 'VirtualParameters.') === 0) {
            $virtual_params[] = $parameter;
        } else {
            $regular_params[] = $parameter;
        }
    }

    // Batch summon VirtualParameters if any
    if (!empty($virtual_params)) {
        try {
            $task_data = [
                'name' => 'getParameterValues',
                'parameterNames' => $virtual_params
            ];

            $task_response = makeGenieRequest($device_tasks_url, 'POST', $task_data);

            if ($task_response['http_code'] === 200 || $task_response['http_code'] === 201 || $task_response['http_code'] === 202) {
                $summon_count += count($virtual_params);
            } else {
                $failed_count += count($virtual_params);
                error_log("Failed to summon VirtualParameters, HTTP Code: " . $task_response['http_code']);
            }
        } catch (Exception $e) {
            $failed_count += count($virtual_params);
            error_log("Exception while summoning VirtualParameters: " . $e->getMessage());
        }

        // Small delay before next batch
        usleep(100000); // 0.1 second
    }

    // Batch summon regular parameters if any
    if (!empty($regular_params)) {
        try {
            $task_data = [
                'name' => 'getParameterValues',
                'parameterNames' => $regular_params
            ];

            $task_response = makeGenieRequest($device_tasks_url, 'POST', $task_data);

            if ($task_response['http_code'] === 200 || $task_response['http_code'] === 201 || $task_response['http_code'] === 202) {
                $summon_count += count($regular_params);
            } else {
                $failed_count += count($regular_params);
                error_log("Failed to summon regular parameters, HTTP Code: " . $task_response['http_code']);
            }
        } catch (Exception $e) {
            $failed_count += count($regular_params);
            error_log("Exception while summoning regular parameters: " . $e->getMessage());
        }
    }

    // Prepare response based on results
    if ($summon_count > 0) {
        if ($failed_count === 0) {
            $alerts[] = [
                'type' => 'success',
                'message' => "Successfully summoned all $summon_count parameters for device detail"
            ];
        } else {
            $alerts[] = [
                'type' => 'warning',
                'message' => "Summoned $summon_count parameters, $failed_count failed"
            ];
        }
    } else {
        $success = false;
        $alerts[] = [
            'type' => 'error',
            'message' => 'Failed to summon any parameters'
        ];
    }

} catch (Exception $e) {
    $success = false;
    $alerts[] = [
        'type' => 'error',
        'message' => 'Error summoning device: ' . $e->getMessage()
    ];
    error_log("Summon detail device error: " . $e->getMessage());
}

// Return response
echo json_encode([
    'success' => $success,
    'status' => $success ? 'success' : 'error',
    'alerts' => $alerts,
    'device_id' => $device_id,
    'summoned_count' => $summon_count,
    'failed_count' => $failed_count,
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
