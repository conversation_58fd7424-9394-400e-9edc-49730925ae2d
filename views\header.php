<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GenieACS Panel - <?php echo $page_title; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563EB',
                        secondary: '#1D4ED8',
                        accent: '#F59E0B',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Fixed Layout Styles */
        .layout-container {
            height: 100vh;
            overflow: hidden;
        }

        .main-content {
            height: calc(100vh - 160px); /* 80px header + 80px footer */
            overflow-y: auto;
            overflow-x: hidden;
            padding-bottom: 80px; /* Space for footer */
        }

        .content-wrapper {
            min-height: calc(100vh - 240px); /* 80px header + 80px footer + padding */
            padding: 2rem;
        }

        /* Mobile adjustments - footer hidden on mobile */
        @media (max-width: 768px) {
            .main-content {
                height: calc(100vh - 80px); /* Only header height, no footer */
                padding-bottom: 20px; /* Reduced padding since no footer */
            }

            .content-wrapper {
                padding: 1rem;
                min-height: calc(100vh - 180px);
            }
        }



        .fixed-footer {
            position: fixed;
            bottom: 0;
            right: 0;
            left: 0;
            z-index: 20;
        }

        @media (min-width: 1024px) {
            .fixed-footer {
                left: 320px; /* 80 * 4 = 320px sidebar width */
            }
        }

        /* Ensure mobile header is properly sized */
        .mobile-header {
            min-height: 80px;
            height: 80px;
        }

        /* Tablet and desktop - footer visible */
        @media (min-width: 769px) {
            .main-content {
                height: calc(100vh - 160px); /* Header + footer */
                min-height: calc(100vh - 160px);
            }
        }






    </style>
</head>
<body class="bg-gray-100 font-sans antialiased">
    <div id="app" class="layout-container flex">
        <!-- Mobile menu overlay -->
        <div id="mobile-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 hidden lg:hidden"></div>
