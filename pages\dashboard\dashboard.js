// Dashboard specific JavaScript functions

// Refresh activity function
function refreshActivity() {
    console.log('Refreshing activity...');
    // Add loading state
    const refreshBtn = document.querySelector('[onclick="refreshActivity()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Refreshing...';
    
    // Simulate API call
    setTimeout(() => {
        refreshBtn.innerHTML = originalText;
        console.log('Activity refreshed');
    }, 2000);
}

// Quick action functions
function addDevice() {
    console.log('Add device clicked');
    alert('Add Device functionality - Connect to GenieACS API');
}

function backupConfig() {
    console.log('Backup config clicked');
    alert('Backup Config functionality - Connect to GenieACS API');
}

function refreshAll() {
    console.log('Refresh all clicked');
    alert('Refresh All functionality - Connect to GenieACS API');
}

function viewReports() {
    console.log('View reports clicked');
    alert('View Reports functionality - Connect to GenieACS API');
}

// Auto refresh stats every 30 seconds
function autoRefreshStats() {
    // Simulate updating stats
    const totalDevices = document.getElementById('total-devices');
    const onlineDevices = document.getElementById('online-devices');
    const offlineDevices = document.getElementById('offline-devices');
    const pendingTasks = document.getElementById('pending-tasks');
    
    if (totalDevices && onlineDevices && offlineDevices && pendingTasks) {
        // Add some random variation to simulate real data
        const variation = Math.floor(Math.random() * 10) - 5;
        
        // Update values (in real app, this would come from API)
        console.log('Auto refreshing dashboard stats...');
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard page loaded');
    
    // Start auto refresh
    setInterval(autoRefreshStats, 30000);
    
    // Add fade-in animation
    const dashboardContent = document.querySelector('.fade-in');
    if (dashboardContent) {
        dashboardContent.style.opacity = '0';
        setTimeout(() => {
            dashboardContent.style.transition = 'opacity 0.5s ease-in-out';
            dashboardContent.style.opacity = '1';
        }, 100);
    }
});
