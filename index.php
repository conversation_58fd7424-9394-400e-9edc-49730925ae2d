<?php
// Get the current page from URL parameter, default to dashboard
$page = isset($_GET['page']) ? $_GET['page'] : (isset($_GET['pages']) ? $_GET['pages'] : 'dashboard');

// Validate page to prevent directory traversal
$allowed_pages = ['dashboard', 'device', 'device-detail', 'settings'];
if (!in_array($page, $allowed_pages)) {
    $page = 'dashboard';
}

// Set page title based on current page
$page_titles = [
    'dashboard' => 'Dashboard',
    'device' => 'Device Management',
    'device-detail' => 'Device Detail',
    'settings' => 'Settings'
];

$page_title = $page_titles[$page];

// Include header
include 'views/header.php';

// Include sidebar
include 'views/sidebar.php';

// Include page content
include "pages/{$page}/{$page}.php";

// Include footer
include 'views/footer.php';
?>
