// Device management specific JavaScript functions

// Global variables
let devicesData = [];
let filteredData = [];
let currentPage = 1;
let itemsPerPage = 20;
let searchQuery = '';

// Load devices data on page load
document.addEventListener('DOMContentLoaded', function() {
    // Debug: Check if main.js is loaded
    console.log('Device page loaded. GenieACS available:', !!window.GenieACS);

    // Initialize stats with loading state
    showStatsLoading();

    // Load devices data
    loadDevices();

    // Start auto refresh every 60 seconds
    setInterval(autoRefreshDeviceStats, 60000);

    // Add fade-in animation
    const deviceContent = document.querySelector('.fade-in');
    if (deviceContent) {
        deviceContent.style.opacity = '0';
        setTimeout(() => {
            deviceContent.style.transition = 'opacity 0.5s ease-in-out';
            deviceContent.style.opacity = '1';
        }, 100);
    }
});

// Load devices from API
function loadDevices() {
    // Show loading state for stats
    showStatsLoading();

    fetch('SolusiDigital/getdevice.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            devicesData = data;
            filteredData = data;
            updateStats();
            renderTable();
            renderPagination();
        })
        .catch(error => {
            console.error('Error loading devices:', error);
            showError('Failed to load devices. Please try again.');
            // Reset stats to 0 on error
            resetStats();
        });
}

// Show loading state for stats
function showStatsLoading() {
    const excellentEl = document.getElementById('excellent-signal');
    const fairEl = document.getElementById('fair-signal');
    const poorEl = document.getElementById('poor-signal');
    const totalDevicesEl = document.getElementById('total-devices-count');

    if (excellentEl) excellentEl.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';
    if (fairEl) fairEl.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';
    if (poorEl) poorEl.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';
    if (totalDevicesEl) totalDevicesEl.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';
}

// Reset stats to 0
function resetStats() {
    const excellentEl = document.getElementById('excellent-signal');
    const fairEl = document.getElementById('fair-signal');
    const poorEl = document.getElementById('poor-signal');
    const totalDevicesEl = document.getElementById('total-devices-count');

    if (excellentEl) excellentEl.textContent = '0';
    if (fairEl) fairEl.textContent = '0';
    if (poorEl) poorEl.textContent = '0';
    if (totalDevicesEl) totalDevicesEl.textContent = '0';
}

// Update signal stats
function updateStats() {
    let excellent = 0, fair = 0, poor = 0;
    let totalActiveDevices = 0;

    devicesData.forEach(device => {
        const rxpower = parseFloat(device.rxpower);
        if (rxpower && rxpower >= -21) {
            excellent++;
        } else if (rxpower && rxpower >= -25) {
            fair++;
        } else if (rxpower) {
            poor++;
        }

        // Tambahkan activedevices ke total
        const activeDevices = parseInt(device.activedevices) || 0;
        totalActiveDevices += activeDevices;
    });

    const excellentEl = document.getElementById('excellent-signal');
    const fairEl = document.getElementById('fair-signal');
    const poorEl = document.getElementById('poor-signal');

    if (excellentEl) excellentEl.textContent = excellent;
    if (fairEl) fairEl.textContent = fair;
    if (poorEl) poorEl.textContent = poor;

    // Update total devices count dengan activedevices
    updateTotalDevicesCount(totalActiveDevices);
}

// Device action functions
function summonDevice(deviceId, button) {
    const btnText = button.querySelector('.btn-text');
    const icon = button.querySelector('i');
    const originalText = btnText ? btnText.textContent : '';
    const originalIcon = icon ? icon.className : '';

    // Show loading state
    if (btnText) btnText.textContent = 'Summoning...';
    if (icon) icon.className = 'fas fa-spinner fa-spin mr-1.5';
    button.disabled = true;
    button.classList.add('opacity-50');

    // Highlight the device row being summoned
    const deviceRow = button.closest('tr');
    if (deviceRow) {
        deviceRow.classList.add('bg-blue-50', 'border-blue-200');
    }

    // Call summon API
    fetch('SolusiDigital/summon-device.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Reset button state
        if (btnText) btnText.textContent = originalText;
        if (icon) icon.className = originalIcon;
        button.disabled = false;
        button.classList.remove('opacity-50');

        // Remove highlight from device row
        const deviceRow = button.closest('tr');
        if (deviceRow) {
            deviceRow.classList.remove('bg-blue-50', 'border-blue-200');
            deviceRow.classList.add('bg-green-50', 'border-green-200');
            // Remove success highlight after 2 seconds
            setTimeout(() => {
                deviceRow.classList.remove('bg-green-50', 'border-green-200');
            }, 2000);
        }

        if (data.success) {
            // Show alerts from server
            if (data.alerts && data.alerts.length > 0) {
                data.alerts.forEach(alert => {
                    showNotification(alert.message, alert.type);
                });
            } else {
                showNotification('Device summoned successfully! Refreshing data...', 'success');
            }

            console.log('Summon results:', data);

            // Langsung refresh data dari getdevice.php
            loadDevices();

        } else {
            // Show error alerts from server
            if (data.alerts && data.alerts.length > 0) {
                data.alerts.forEach(alert => {
                    showNotification(alert.message, alert.type);
                });
            } else {
                throw new Error('Summon failed');
            }
        }
    })
    .catch(error => {
        // Reset button state
        if (btnText) btnText.textContent = originalText;
        if (icon) icon.className = originalIcon;
        button.disabled = false;
        button.classList.remove('opacity-50');

        // Remove highlight and show error state
        const deviceRow = button.closest('tr');
        if (deviceRow) {
            deviceRow.classList.remove('bg-blue-50', 'border-blue-200');
            deviceRow.classList.add('bg-red-50', 'border-red-200');
            // Remove error highlight after 3 seconds
            setTimeout(() => {
                deviceRow.classList.remove('bg-red-50', 'border-red-200');
            }, 3000);
        }

        console.error('Summon error:', error);
        showNotification(`Failed to summon device: ${error.message}`, 'error');
    });
}

// Refresh specific device data after summon
function refreshDeviceData(deviceId) {
    console.log(`Refreshing data for device: ${deviceId}`);
    // Data akan diupdate melalui loadDevices() yang fetch dari getdevice.php
}

function viewDevice(deviceId) {
    // Redirect to device detail page
    // Encode the deviceId to preserve special characters in URL
    window.location.href = `?page=device-detail&id=${encodeURIComponent(deviceId)}`;
}

function deleteDevice(deviceId) {
    if (confirm('Are you sure you want to delete this device?')) {
        // Simulate delete API call
        showNotification('Device deleted successfully!', 'success');
        // Reload data
        loadDevices();
    }
}

// Render table and mobile cards
function renderTable() {
    const tbody = document.getElementById('device-table-body');
    const mobileCards = document.getElementById('device-mobile-cards');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    if (pageData.length === 0) {
        // Desktop table empty state
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="px-6 py-8 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-search text-gray-400 text-3xl mb-4"></i>
                        <p class="text-gray-500">No devices found</p>
                    </div>
                </td>
            </tr>
        `;

        // Mobile cards empty state
        mobileCards.innerHTML = `
            <div class="space-y-4 p-4">
                <div class="flex flex-col items-center justify-center py-8">
                    <i class="fas fa-search text-gray-400 text-3xl mb-4"></i>
                    <p class="text-gray-500">No devices found</p>
                </div>
            </div>
        `;
        return;
    }

    tbody.innerHTML = pageData.map(device => {
        const rxpower = parseFloat(device.rxpower);
        const rxpowerClass = getRxPowerClass(rxpower);
        const statusClass = getStatusClass(device._lastInform);
        const ssids = getAvailableSSIDs(device);
        const ssidTooltip = getSSIDTooltip(device);
        const lastInform = formatLastInform(device._lastInform);

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${device.productclass || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${rxpowerClass.bg} ${rxpowerClass.text} border ${rxpowerClass.border}">
                        <i class="fas fa-signal mr-1.5"></i>
                        ${device.rxpower ? device.rxpower + ' dBm' : 'N/A'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${statusClass.bg} ${statusClass.text} border ${statusClass.border}">
                        <i class="fas ${statusClass.icon} mr-1.5"></i>
                        ${statusClass.label}
                    </span>
                </td>
                <td class="mobile-hide px-6 py-4 whitespace-nowrap text-sm text-gray-500">${device.pppoe || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" title="${ssidTooltip}">
                    <span class="cursor-help">${ssids}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${device.activedevices || '0'}</td>
                <td class="mobile-hide px-6 py-4 whitespace-nowrap text-sm text-gray-500">${device.wanbridge || 'N/A'}</td>
                <td class="mobile-hide px-6 py-4 whitespace-nowrap text-sm text-gray-500">${lastInform}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="btn-group flex items-center space-x-2">
                        <button id="summon-btn-${device._id}" onclick="summonDevice('${device._id}', this)" class="inline-flex items-center px-3 py-2 bg-purple-600 text-white text-xs font-medium rounded-lg hover:bg-purple-700 transition-all duration-200 shadow-sm hover:shadow-md" title="Summon Device">
                            <i class="fas fa-sync mr-1.5"></i>
                            <span class="btn-text">Summon</span>
                        </button>
                        <button onclick="viewDevice('${device._id}')" class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md" title="View Device">
                            <i class="fas fa-eye mr-1.5"></i>
                            <span class="btn-text">View</span>
                        </button>
                        <button onclick="deleteDevice('${device._id}')" class="inline-flex items-center px-3 py-2 bg-red-600 text-white text-xs font-medium rounded-lg hover:bg-red-700 transition-all duration-200 shadow-sm hover:shadow-md" title="Delete Device">
                            <i class="fas fa-trash mr-1.5"></i>
                            <span class="btn-text">Delete</span>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    // Generate mobile cards
    mobileCards.innerHTML = `
        <div class="space-y-4 p-4">
            ${pageData.map(device => {
                const rxpower = parseFloat(device.rxpower);
                const rxpowerClass = getRxPowerClass(rxpower);
                const statusClass = getStatusClass(device._lastInform);
                const ssids = getAvailableSSIDs(device);
                const lastInform = formatLastInform(device._lastInform);

                return `
                    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-semibold text-gray-900">${device.productclass || 'N/A'}</h4>
                                <p class="text-sm text-gray-500">${device.activedevices || '0'} active devices</p>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusClass.bg} ${statusClass.text} border ${statusClass.border}">
                                <i class="fas ${statusClass.icon} mr-1"></i>
                                ${statusClass.label}
                            </span>
                        </div>

                        <div class="grid grid-cols-2 gap-3 text-sm mb-4">
                            <div>
                                <span class="text-gray-600">RX Power:</span>
                                <div class="mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${rxpowerClass.bg} ${rxpowerClass.text} border ${rxpowerClass.border}">
                                        <i class="fas fa-signal mr-1"></i>
                                        ${device.rxpower ? device.rxpower + ' dBm' : 'N/A'}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <span class="text-gray-600">PPPoE:</span>
                                <p class="font-medium">${device.pppoe || 'N/A'}</p>
                            </div>
                            <div>
                                <span class="text-gray-600">SSID:</span>
                                <p class="font-medium">${ssids}</p>
                            </div>
                            <div>
                                <span class="text-gray-600">WAN Bridge:</span>
                                <p class="font-medium">${device.wanbridge || 'N/A'}</p>
                            </div>
                        </div>

                        <div class="text-xs text-gray-500 mb-3">
                            Last Inform: ${lastInform}
                        </div>

                        <div class="flex flex-col sm:flex-row gap-2">
                            <button id="summon-btn-mobile-${device._id}" onclick="summonDevice('${device._id}', this)" class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-purple-600 text-white text-xs font-medium rounded-md hover:bg-purple-700 transition-colors">
                                <i class="fas fa-sync mr-1"></i>
                                Summon
                            </button>
                            <button onclick="viewDevice('${device._id}')" class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-eye mr-1"></i>
                                View
                            </button>
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;

    updatePaginationInfo();
}

// Helper functions
function getAvailableSSIDs(device) {
    // Cek SSID 1-4 secara berurutan, tampilkan yang pertama tersedia
    if (device.ssid1 && device.ssid1.trim() !== '') {
        return device.ssid1.trim();
    }
    if (device.ssid2 && device.ssid2.trim() !== '') {
        return device.ssid2.trim();
    }
    if (device.ssid3 && device.ssid3.trim() !== '') {
        return device.ssid3.trim();
    }
    if (device.ssid4 && device.ssid4.trim() !== '') {
        return device.ssid4.trim();
    }

    // Jika tidak ada SSID yang tersedia
    return 'N/A';
}

function getSSIDTooltip(device) {
    // Buat tooltip yang menampilkan semua SSID yang tersedia
    const ssidList = [];

    if (device.ssid1 && device.ssid1.trim() !== '') {
        ssidList.push(device.ssid1.trim());
    }
    if (device.ssid2 && device.ssid2.trim() !== '') {
        ssidList.push(device.ssid2.trim());
    }
    if (device.ssid3 && device.ssid3.trim() !== '') {
        ssidList.push(device.ssid3.trim());
    }
    if (device.ssid4 && device.ssid4.trim() !== '') {
        ssidList.push(device.ssid4.trim());
    }

    if (ssidList.length === 0) {
        return 'No WiFi networks';
    }

    if (ssidList.length === 1) {
        return ssidList[0];
    }

    return ssidList.join(', ');
}

function getRxPowerClass(rxpower) {
    if (!rxpower) return { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200' };

    if (rxpower >= -21) {
        return { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' };
    } else if (rxpower >= -25) {
        return { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' };
    } else {
        return { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' };
    }
}

function getStatusClass(lastInform) {
    if (!lastInform) {
        return {
            bg: 'bg-gray-100',
            text: 'text-gray-800',
            border: 'border-gray-200',
            icon: 'fa-question-circle',
            label: 'Unknown'
        };
    }

    const now = new Date();
    const lastInformDate = new Date(lastInform);
    const diffMinutes = (now - lastInformDate) / (1000 * 60);

    if (diffMinutes <= 5) {
        return {
            bg: 'bg-green-100',
            text: 'text-green-800',
            border: 'border-green-200',
            icon: 'fa-wifi',
            label: 'Online'
        };
    } else {
        return {
            bg: 'bg-red-100',
            text: 'text-red-800',
            border: 'border-red-200',
            icon: 'fa-times-circle',
            label: 'Offline'
        };
    }
}

function formatLastInform(lastInform) {
    if (!lastInform) return 'Never';

    const now = new Date();
    const lastInformDate = new Date(lastInform);
    const diffMinutes = Math.floor((now - lastInformDate) / (1000 * 60));

    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
}

// Search functionality
function searchDevices(query) {
    searchQuery = query.toLowerCase();

    if (searchQuery === '') {
        filteredData = devicesData;
        document.getElementById('clear-search').classList.add('hidden');
    } else {
        filteredData = devicesData.filter(device => {
            // Search in basic device info
            const basicMatch = (device.productclass && device.productclass.toLowerCase().includes(searchQuery)) ||
                              (device._id && device._id.toLowerCase().includes(searchQuery)) ||
                              (device.pppoe && device.pppoe.toLowerCase().includes(searchQuery));

            // Search in all available SSIDs
            const ssidMatch = (device.ssid1 && device.ssid1.toLowerCase().includes(searchQuery)) ||
                             (device.ssid2 && device.ssid2.toLowerCase().includes(searchQuery)) ||
                             (device.ssid3 && device.ssid3.toLowerCase().includes(searchQuery)) ||
                             (device.ssid4 && device.ssid4.toLowerCase().includes(searchQuery));

            return basicMatch || ssidMatch;
        });
        document.getElementById('clear-search').classList.remove('hidden');
    }

    currentPage = 1;
    renderTable();
    renderPagination();
}

function clearSearch() {
    document.getElementById('device-search').value = '';
    searchDevices('');
}

// Pagination
function renderPagination() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const paginationContainer = document.getElementById('pagination-buttons');

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
        <button onclick="previousPage()" ${currentPage === 1 ? 'disabled' : ''}
                class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}">
            Previous
        </button>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button onclick="goToPage(${i})"
                    class="px-3 py-1 ${i === currentPage ? 'bg-blue-600 text-white' : 'border border-gray-300 hover:bg-gray-50'} rounded text-sm">
                ${i}
            </button>
        `;
    }

    // Next button
    paginationHTML += `
        <button onclick="nextPage()" ${currentPage === totalPages ? 'disabled' : ''}
                class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}">
            Next
        </button>
    `;

    paginationContainer.innerHTML = paginationHTML;
}

function updateTotalDevicesCount(totalActiveDevices) {
    // Update total devices count dengan activedevices
    const totalDevicesEl = document.getElementById('total-devices-count');
    if (totalDevicesEl) {
        totalDevicesEl.textContent = totalActiveDevices;
    }
}

function updatePaginationInfo() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredData.length);

    document.getElementById('showing-from').textContent = filteredData.length > 0 ? startIndex + 1 : 0;
    document.getElementById('showing-to').textContent = endIndex;

    // Total devices count akan diupdate oleh updateStats() menggunakan activedevices
    // Jadi kita tidak perlu update di sini lagi
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        renderTable();
        renderPagination();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        renderTable();
        renderPagination();
    }
}

function goToPage(page) {
    currentPage = page;
    renderTable();
    renderPagination();
}

// Utility functions
function showError(message) {
    const tbody = document.getElementById('device-table-body');
    const mobileCards = document.getElementById('device-mobile-cards');

    // Desktop table error
    tbody.innerHTML = `
        <tr>
            <td colspan="9" class="px-6 py-8 text-center">
                <div class="flex flex-col items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-400 text-3xl mb-4"></i>
                    <p class="text-red-500">${message}</p>
                    <button onclick="loadDevices()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Retry
                    </button>
                </div>
            </td>
        </tr>
    `;

    // Mobile cards error
    mobileCards.innerHTML = `
        <div class="space-y-4 p-4">
            <div class="flex flex-col items-center justify-center py-8">
                <i class="fas fa-exclamation-triangle text-red-400 text-3xl mb-4"></i>
                <p class="text-red-500">${message}</p>
                <button onclick="loadDevices()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Retry
                </button>
            </div>
        </div>
    `;
}

function showNotification(message, type = 'info') {
    // Use the global notification system from main.js
    if (window.GenieACS && window.GenieACS.showNotification) {
        console.log('Device page: Using main.js notification system:', message, type);
        window.GenieACS.showNotification(message, type);
    } else {
        console.log('Device page: GenieACS not available, using fallback notification');
        // Create a simple fallback notification
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-3 rounded-md shadow-lg z-50 ${
            type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
            type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
            type === 'warning' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
            'bg-blue-100 text-blue-800 border border-blue-200'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                } mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Auto refresh device stats
function autoRefreshDeviceStats() {
    console.log('Auto refreshing device stats...');
    // Show subtle loading indicator during auto-refresh
    showStatsLoading();

    // Reload devices data every 60 seconds
    fetch('SolusiDigital/getdevice.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            devicesData = data;
            filteredData = data;
            updateStats();
            renderTable();
            renderPagination();
        })
        .catch(error => {
            console.error('Error auto-refreshing devices:', error);
            // Don't show error on auto-refresh, just reset stats
            resetStats();
        });
}
