// Settings page specific JavaScript functions

// Toggle functions for switches
function toggleNotifications() {
    const toggle = document.getElementById('notifications-toggle');
    const span = toggle.querySelector('span');
    
    if (toggle.classList.contains('bg-blue-600')) {
        toggle.classList.remove('bg-blue-600');
        toggle.classList.add('bg-gray-200');
        span.classList.remove('translate-x-6');
        span.classList.add('translate-x-1');
        console.log('Notifications disabled');
    } else {
        toggle.classList.add('bg-blue-600');
        toggle.classList.remove('bg-gray-200');
        span.classList.add('translate-x-6');
        span.classList.remove('translate-x-1');
        console.log('Notifications enabled');
    }
}

function toggle2FA() {
    const toggle = document.getElementById('2fa-toggle');
    const span = toggle.querySelector('span');
    
    if (toggle.classList.contains('bg-blue-600')) {
        toggle.classList.remove('bg-blue-600');
        toggle.classList.add('bg-gray-200');
        span.classList.remove('translate-x-6');
        span.classList.add('translate-x-1');
        console.log('2FA disabled');
    } else {
        toggle.classList.add('bg-blue-600');
        toggle.classList.remove('bg-gray-200');
        span.classList.add('translate-x-6');
        span.classList.remove('translate-x-1');
        console.log('2FA enabled');
    }
}

// Form submission handlers
function handleGeneralSettingsSubmit(event) {
    event.preventDefault();
    
    const formData = {
        serverName: document.getElementById('server-name').value,
        refreshInterval: document.getElementById('refresh-interval').value,
        timezone: document.getElementById('timezone').value,
        notifications: document.getElementById('notifications-toggle').classList.contains('bg-blue-600')
    };
    
    console.log('Saving general settings:', formData);
    
    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        alert('General settings saved successfully!');
    }, 2000);
}

function handleSecuritySettingsSubmit(event) {
    event.preventDefault();
    
    const formData = {
        twoFactorAuth: document.getElementById('2fa-toggle').classList.contains('bg-blue-600'),
        sessionTimeout: document.getElementById('session-timeout').value,
        passwordPolicy: {
            minLength: document.getElementById('min-length').checked,
            uppercase: document.getElementById('uppercase').checked,
            specialChars: document.getElementById('special-chars').checked
        }
    };
    
    console.log('Saving security settings:', formData);
    
    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        alert('Security settings updated successfully!');
    }, 2000);
}

// System info update functions
function updateSystemInfo() {
    console.log('Updating system information...');
    
    // In real app, this would fetch data from GenieACS API
    // For now, simulate some updates
    
    const uptime = document.getElementById('system-uptime');
    const memoryUsage = document.getElementById('memory-usage');
    
    if (uptime && memoryUsage) {
        // Simulate uptime increment
        console.log('System info updated');
    }
}

// Auto refresh system info
function autoRefreshSystemInfo() {
    updateSystemInfo();
}

// Initialize settings page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Settings page loaded');
    
    // Add form event listeners
    const generalForm = document.getElementById('general-settings-form');
    const securityForm = document.getElementById('security-settings-form');
    
    if (generalForm) {
        generalForm.addEventListener('submit', handleGeneralSettingsSubmit);
    }
    
    if (securityForm) {
        securityForm.addEventListener('submit', handleSecuritySettingsSubmit);
    }
    
    // Start auto refresh for system info every 30 seconds
    setInterval(autoRefreshSystemInfo, 30000);
    
    // Add fade-in animation
    const settingsContent = document.querySelector('.fade-in');
    if (settingsContent) {
        settingsContent.style.opacity = '0';
        setTimeout(() => {
            settingsContent.style.transition = 'opacity 0.5s ease-in-out';
            settingsContent.style.opacity = '1';
        }, 100);
    }
    
    // Load saved settings from localStorage (if any)
    loadSavedSettings();
});

// Load saved settings
function loadSavedSettings() {
    const savedSettings = localStorage.getItem('genieacs-settings');
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);
            
            // Apply saved general settings
            if (settings.serverName) {
                document.getElementById('server-name').value = settings.serverName;
            }
            if (settings.refreshInterval) {
                document.getElementById('refresh-interval').value = settings.refreshInterval;
            }
            if (settings.timezone) {
                document.getElementById('timezone').value = settings.timezone;
            }
            
            console.log('Loaded saved settings');
        } catch (e) {
            console.error('Error loading saved settings:', e);
        }
    }
}

// Save settings to localStorage
function saveSettingsToLocal(settings) {
    try {
        localStorage.setItem('genieacs-settings', JSON.stringify(settings));
        console.log('Settings saved to localStorage');
    } catch (e) {
        console.error('Error saving settings to localStorage:', e);
    }
}
