// Device Detail JavaScript
var deviceDetailData = null;
var currentDetailTab = 'wan';

// Load device detail on page load
document.addEventListener('DOMContentLoaded', function() {
    // Debug: Check if main.js is loaded
    console.log('Device detail loaded. GenieACS available:', !!window.GenieACS);

    // Try to get device ID from URL or window.deviceId
    const deviceId = getDeviceIdFromURL() || window.deviceId;

    if (deviceId) {
        window.deviceId = deviceId; // Ensure it's set
        loadDeviceDetail();

        // Auto refresh every 30 seconds (with safety check)
        setInterval(() => {
            if (getDeviceIdFromURL() || window.deviceId) {
                loadDeviceDetail();
            }
        }, 30000);
    } else {
        console.log('Device ID not available on page load - this is normal for back/refresh');
        // Don't show error immediately - wait a bit for URL to be parsed
        setTimeout(() => {
            const retryDeviceId = getDeviceIdFromURL() || window.deviceId;
            if (retryDeviceId) {
                window.deviceId = retryDeviceId;
                loadDeviceDetail();
                setInterval(() => {
                    if (getDeviceIdFromURL() || window.deviceId) {
                        loadDeviceDetail();
                    }
                }, 30000);
            } else {
                showError('Device ID not found');
            }
        }, 100);
    }
});

// Load device detail data
function loadDeviceDetail() {
    // Get device ID from URL or window.deviceId
    const deviceId = getDeviceIdFromURL() || window.deviceId;

    // Check if device ID is available
    if (!deviceId) {
        console.log('Device ID not available yet, skipping load');
        return; // Silent return - tidak error karena normal saat back/refresh
    }

    const url = `SolusiDigital/getdetaildevice.php?id=${encodeURIComponent(deviceId)}`;

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text(); // Get as text first to debug
        })
        .then(text => {
            try {
                const data = JSON.parse(text);

                if (data && !data.error) {
                    deviceDetailData = data; // getdetaildevice.php returns single device object
                    window.deviceId = deviceId; // Ensure window.deviceId is set
                    renderDeviceDetail();
                } else {
                    throw new Error(data.error || 'Device not found');
                }
            } catch (parseError) {
                throw new Error('Invalid JSON response: ' + text.substring(0, 100));
            }
        })
        .catch(error => {
            // Only show error if device ID was available (real error)
            if (deviceId) {
                console.error('Error loading device detail:', error);
                showError('Failed to load device details: ' + error.message);
            }
        });
}

// Render device detail
function renderDeviceDetail() {
    if (!deviceDetailData) return;
    // Update device status
    updateDeviceStatus();
    
    // Update stats cards
    updateStatsCards();
    
    // Update tab content based on current tab
    updateTabContent();
}

// Update device status indicator
function updateDeviceStatus() {
    const statusEl = document.getElementById('device-status');
    const lastInform = deviceDetailData.connectionInfo._lastInform;
    const statusClass = getStatusClass(lastInform);

    statusEl.innerHTML = `
        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${statusClass.bg} ${statusClass.text} border ${statusClass.border}">
            <i class="fas ${statusClass.icon} mr-1.5"></i>
            ${statusClass.label}
        </span>
    `;
}

// Update stats cards
function updateStatsCards() {
    const statsContainer = document.getElementById('device-stats');
    const rxpower = parseFloat(deviceDetailData.monitoringInfo.rxpower.value) || 0;
    const rxpowerClass = getRxPowerClass(rxpower);
    const activeDevices = parseInt(deviceDetailData.monitoringInfo.activedevices.value) || 0;
    const lastInform = formatLastInform(deviceDetailData.connectionInfo._lastInform);
    
    statsContainer.innerHTML = `
        <!-- RX Power -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">RX Power</p>
                    <p class="text-2xl font-bold ${rxpowerClass.text}">${deviceDetailData.monitoringInfo.rxpower.value ? deviceDetailData.monitoringInfo.rxpower.value + ' dBm' : 'N/A'}</p>
                    <p class="text-xs ${rxpowerClass.text} mt-1">${rxpowerClass.label}</p>
                </div>
                <div class="p-3 ${rxpowerClass.bg} rounded-xl">
                    <i class="fas fa-signal ${rxpowerClass.text} text-xl"></i>
                </div>
            </div>
        </div>
        
        <!-- Active Devices -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Devices</p>
                    <p class="text-2xl font-bold text-blue-600">${activeDevices}</p>
                    <p class="text-xs text-blue-600 mt-1">connected</p>
                </div>
                <div class="p-3 bg-blue-100 rounded-xl">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <!-- Temperature -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Temperature</p>
                    <p class="text-2xl font-bold text-orange-600">${deviceDetailData.monitoringInfo.temperature.value || 'N/A'}</p>
                    <p class="text-xs text-orange-600 mt-1">celsius</p>
                </div>
                <div class="p-3 bg-orange-100 rounded-xl">
                    <i class="fas fa-thermometer-half text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <!-- Last Inform -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Last Inform</p>
                    <p class="text-lg font-bold text-gray-900">${lastInform}</p>
                    <p class="text-xs text-gray-600 mt-1">last contact</p>
                </div>
                <div class="p-3 bg-gray-100 rounded-xl">
                    <i class="fas fa-clock text-gray-600 text-xl"></i>
                </div>
            </div>
        </div>
    `;
}

// Update tab content
function updateTabContent() {
    switch(currentDetailTab) {
        case 'wan':
            updateWANTab();
            break;
        case 'wifi':
            updateWiFiTab();
            break;
        case 'system':
            updateSystemTab();
            break;
    }
}



// Update WAN tab dengan format tabel GenieACS menggunakan data terbaru
function updateWANTab() {
    const wanInfoEl = document.getElementById('wan-info');

    // Clear existing content first to prevent stacking
    if (wanInfoEl) {
        wanInfoEl.innerHTML = '';
    }

    // Helper function to create parameter row for WAN IP (tanpa MAC Address, Subnet Mask, Gateway, DNS)
    function createParameterRow(enable, name, vlan, mode, ipAddress, service, nat, type, lan1, lan2, lan3, lan4, ssid1, ssid2, ssid3, ssid4, connectionData) {
        return `
            <tr class="hover:bg-gray-50 border-b border-gray-200">
                <td class="px-3 py-2 text-center">
                    <input type="checkbox" ${enable ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" disabled>
                </td>
                <td class="px-3 py-2 text-sm font-medium text-gray-900">${name || 'N/A'}</td>
                <td class="px-3 py-2 text-sm text-center">${vlan || 'N/A'}</td>
                <td class="px-3 py-2 text-sm">${mode || 'N/A'}</td>
                <td class="px-3 py-2 text-sm font-mono text-blue-600">${ipAddress || 'N/A'}</td>
                <td class="px-3 py-2 text-sm">${service || 'N/A'}</td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium ${nat ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                        ${nat ? 'Enabled' : 'Disabled'}
                    </span>
                </td>
                <td class="px-3 py-2 text-sm">${type || 'N/A'}</td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan1 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan2 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan3 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan3 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan4 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan4 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid1 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid2 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid3 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid3 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid4 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid4 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <button onclick="editWANConnection('${connectionData.type}', '${connectionData.path}')"
                            class="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded hover:bg-blue-700 transition-colors whitespace-nowrap">
                        <i class="fas fa-edit mr-1"></i>
                        <span class="hidden sm:inline">Edit</span>
                        <span class="sm:hidden">Edit</span>
                    </button>
                </td>
            </tr>
        `;
    }

    // Helper function to create parameter row for WAN PPP (dengan Username)
    function createPPPParameterRow(enable, name, vlan, mode, ipAddress, username, service, nat, type, lan1, lan2, lan3, lan4, ssid1, ssid2, ssid3, ssid4, connectionData) {
        return `
            <tr class="hover:bg-gray-50 border-b border-gray-200">
                <td class="px-3 py-2 text-center">
                    <input type="checkbox" ${enable ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" disabled>
                </td>
                <td class="px-3 py-2 text-sm font-medium text-gray-900">${name || 'N/A'}</td>
                <td class="px-3 py-2 text-sm text-center">${vlan || 'N/A'}</td>
                <td class="px-3 py-2 text-sm">${mode || 'N/A'}</td>
                <td class="px-3 py-2 text-sm font-mono text-blue-600">${ipAddress || 'N/A'}</td>
                <td class="px-3 py-2 text-sm font-mono text-purple-600">${username || 'N/A'}</td>
                <td class="px-3 py-2 text-sm">${service || 'N/A'}</td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium ${nat ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                        ${nat ? 'Enabled' : 'Disabled'}
                    </span>
                </td>
                <td class="px-3 py-2 text-sm">${type || 'N/A'}</td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan1 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan2 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan3 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan3 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${lan4 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${lan4 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid1 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid2 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid3 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid3 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <span class="inline-flex items-center px-1 py-0.5 rounded text-xs ${ssid4 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
                        ${ssid4 ? '✓' : '✗'}
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    <button onclick="editWANConnection('${connectionData.type}', '${connectionData.path}')"
                            class="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded hover:bg-blue-700 transition-colors whitespace-nowrap">
                        <i class="fas fa-edit mr-1"></i>
                        <span class="hidden sm:inline">Edit</span>
                        <span class="sm:hidden">Edit</span>
                    </button>
                </td>
            </tr>
        `;
    }

    // Build WAN IP Connection rows dan mobile cards menggunakan data terbaru
    let wanIPRows = '';
    let wanIPMobileCards = '';
    if (deviceDetailData.wanConnections.wanIPConnections && deviceDetailData.wanConnections.wanIPConnections.length > 0) {
        const ipData = deviceDetailData.wanConnections.wanIPConnections.map((conn) => {
            // Extract LAN binding information
            let lan1 = false, lan2 = false, lan3 = false, lan4 = false;
            let ssid1 = false, ssid2 = false, ssid3 = false, ssid4 = false;

            if (conn.lanBinding) {
                if (conn.lanBinding.type === 'huawei') {
                    // For Huawei, check individual enable flags (value 1 = enable, 0 = disable)
                    lan1 = conn.lanBinding.lan1Enable?.value === 1 || conn.lanBinding.lan1Enable?.value === '1';
                    lan2 = conn.lanBinding.lan2Enable?.value === 1 || conn.lanBinding.lan2Enable?.value === '1';
                    lan3 = conn.lanBinding.lan3Enable?.value === 1 || conn.lanBinding.lan3Enable?.value === '1';
                    lan4 = conn.lanBinding.lan4Enable?.value === 1 || conn.lanBinding.lan4Enable?.value === '1';
                    ssid1 = conn.lanBinding.ssid1Enable?.value === 1 || conn.lanBinding.ssid1Enable?.value === '1';
                    ssid2 = conn.lanBinding.ssid2Enable?.value === 1 || conn.lanBinding.ssid2Enable?.value === '1';
                    ssid3 = conn.lanBinding.ssid3Enable?.value === 1 || conn.lanBinding.ssid3Enable?.value === '1';
                    ssid4 = conn.lanBinding.ssid4Enable?.value === 1 || conn.lanBinding.ssid4Enable?.value === '1';
                } else if (conn.lanBinding.type === 'zte') {
                    // For ZTE, parse the TR-069 path string
                    // e.g., "InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1,InternetGatewayDevice.LANDevice.1.WLANConfiguration.1"
                    const interfaceString = conn.lanBinding.ctComInterface?.value || conn.lanBinding.cmccInterface?.value || '';
                    const interfaces = interfaceString.split(',');

                    // Check LAN interfaces
                    lan1 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.1'));
                    lan2 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.2'));
                    lan3 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.3'));
                    lan4 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.4'));

                    // Check WLAN/SSID interfaces
                    ssid1 = interfaces.some(iface => iface.includes('WLANConfiguration.1'));
                    ssid2 = interfaces.some(iface => iface.includes('WLANConfiguration.2'));
                    ssid3 = interfaces.some(iface => iface.includes('WLANConfiguration.3'));
                    ssid4 = interfaces.some(iface => iface.includes('WLANConfiguration.4'));

                }
            }

            // Get actual service type from backend data
            let serviceType = 'N/A'; // default

            // First check if we have actual service list data from backend
            if (conn.serviceList) {
                if (conn.serviceList.serviceList?.value) {
                    serviceType = conn.serviceList.serviceList.value;
                } else if (conn.serviceList.ctComServiceList?.value) {
                    serviceType = conn.serviceList.ctComServiceList.value;
                } else if (conn.serviceList.cmccServiceList?.value) {
                    serviceType = conn.serviceList.cmccServiceList.value;
                }
            }

            // If no service data from backend, try to determine from connection name
            if (serviceType === 'N/A' && conn.name?.value) {
                const nameLower = conn.name.value.toLowerCase();
                if (nameLower.includes('iptv') || nameLower.includes('tv') || nameLower.includes('multicast')) {
                    serviceType = 'IPTV';
                } else if (nameLower.includes('voip') || nameLower.includes('voice') || nameLower.includes('sip')) {
                    serviceType = 'VOIP';
                } else if (nameLower.includes('tr069') || nameLower.includes('manage') || nameLower.includes('cwmp')) {
                    serviceType = 'TR069';
                } else if (nameLower.includes('internet') || nameLower.includes('wan') || nameLower.includes('pppoe')) {
                    serviceType = 'INTERNET';
                }
            }

            // If still N/A, default to INTERNET
            if (serviceType === 'N/A') {
                serviceType = 'INTERNET';
            }

            // Get actual VLAN value
            let vlanValue = 'N/A';
            if (conn.vlanInfo) {
                if (conn.vlanInfo.vlanId?.value) {
                    vlanValue = conn.vlanInfo.vlanId.value;
                } else if (conn.vlanInfo.ctComVlanId?.value) {
                    vlanValue = conn.vlanInfo.ctComVlanId.value;
                } else if (conn.vlanInfo.cmccVlanId?.value) {
                    vlanValue = conn.vlanInfo.cmccVlanId.value;
                }
            }

            // Build LAN binding text
            const lanPorts = [];
            if (lan1) lanPorts.push('LAN1');
            if (lan2) lanPorts.push('LAN2');
            if (lan3) lanPorts.push('LAN3');
            if (lan4) lanPorts.push('LAN4');
            if (ssid1) lanPorts.push('SSID1');
            if (ssid2) lanPorts.push('SSID2');
            if (ssid3) lanPorts.push('SSID3');
            if (ssid4) lanPorts.push('SSID4');
            const lanBindingText = lanPorts.length > 0 ? lanPorts.join(', ') : 'None';

            return {
                tableRow: createParameterRow(
                    conn.enable?.value === 'true' || conn.enable?.value === true || conn.enable?.value === 1,
                    conn.name?.value || 'N/A',
                    vlanValue,
                    conn.addressingType?.value || conn.connectionType?.value || 'DHCP',
                    conn.externalIPAddress?.value || 'N/A',
                    serviceType,
                    conn.natEnabled?.value === 'true' || conn.natEnabled?.value === true || conn.natEnabled?.value === 1,
                    'IP_Routed',
                    lan1, lan2, lan3, lan4,
                    ssid1, ssid2, ssid3, ssid4,
                    {
                        type: 'WANIPConnection',
                        path: conn.path || '',
                        name: conn.name?.value || 'N/A'
                    }
                ),
                mobileCard: createMobileCard({
                    name: conn.name?.value || 'N/A',
                    vlan: vlanValue,
                    type: conn.addressingType?.value || conn.connectionType?.value || 'DHCP',
                    ip: conn.externalIPAddress?.value || 'N/A',
                    service: serviceType,
                    nat: (conn.natEnabled?.value === 'true' || conn.natEnabled?.value === true || conn.natEnabled?.value === 1) ? 'Enabled' : 'Disabled',
                    connectionType: 'IP_Routed',
                    lanBinding: lanBindingText,
                    path: conn.path || '',
                    connectionTypeForEdit: 'WANIPConnection',
                    enabled: conn.enable?.value === 'true' || conn.enable?.value === true || conn.enable?.value === 1
                })
            };
        });

        wanIPRows = ipData.map(item => item.tableRow).join('');
        wanIPMobileCards = ipData.map(item => item.mobileCard).join('');
    }

    // Build WAN PPP Connection rows dan mobile cards menggunakan data terbaru
    let wanPPPRows = '';
    let wanPPPMobileCards = '';
    if (deviceDetailData.wanConnections.wanPPPConnections && deviceDetailData.wanConnections.wanPPPConnections.length > 0) {
        const pppData = deviceDetailData.wanConnections.wanPPPConnections.map((conn) => {
            // Extract LAN binding information for PPP connections
            let lan1 = false, lan2 = false, lan3 = false, lan4 = false;
            let ssid1 = false, ssid2 = false, ssid3 = false, ssid4 = false;

            if (conn.lanBinding) {
                if (conn.lanBinding.type === 'huawei') {
                    // For Huawei PPP, check individual enable flags (value 1 = enable, 0 = disable)
                    lan1 = conn.lanBinding.lan1Enable?.value === 1 || conn.lanBinding.lan1Enable?.value === '1';
                    lan2 = conn.lanBinding.lan2Enable?.value === 1 || conn.lanBinding.lan2Enable?.value === '1';
                    lan3 = conn.lanBinding.lan3Enable?.value === 1 || conn.lanBinding.lan3Enable?.value === '1';
                    lan4 = conn.lanBinding.lan4Enable?.value === 1 || conn.lanBinding.lan4Enable?.value === '1';
                    ssid1 = conn.lanBinding.ssid1Enable?.value === 1 || conn.lanBinding.ssid1Enable?.value === '1';
                    ssid2 = conn.lanBinding.ssid2Enable?.value === 1 || conn.lanBinding.ssid2Enable?.value === '1';
                    ssid3 = conn.lanBinding.ssid3Enable?.value === 1 || conn.lanBinding.ssid3Enable?.value === '1';
                    ssid4 = conn.lanBinding.ssid4Enable?.value === 1 || conn.lanBinding.ssid4Enable?.value === '1';
                } else if (conn.lanBinding.type === 'zte') {
                    // For ZTE PPP, parse the TR-069 path string
                    // e.g., "InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1,InternetGatewayDevice.LANDevice.1.WLANConfiguration.1"
                    const interfaceString = conn.lanBinding.ctComInterface?.value || conn.lanBinding.cmccInterface?.value || '';
                    const interfaces = interfaceString.split(',');

                    // Check LAN interfaces
                    lan1 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.1'));
                    lan2 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.2'));
                    lan3 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.3'));
                    lan4 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.4'));

                    // Check WLAN/SSID interfaces
                    ssid1 = interfaces.some(iface => iface.includes('WLANConfiguration.1'));
                    ssid2 = interfaces.some(iface => iface.includes('WLANConfiguration.2'));
                    ssid3 = interfaces.some(iface => iface.includes('WLANConfiguration.3'));
                    ssid4 = interfaces.some(iface => iface.includes('WLANConfiguration.4'));

                }
            }

            // Get actual service type from backend data for PPP
            let serviceType = 'N/A'; // default

            // First check if we have actual service list data from backend
            if (conn.serviceList) {
                if (conn.serviceList.serviceList?.value) {
                    serviceType = conn.serviceList.serviceList.value;
                } else if (conn.serviceList.ctComServiceList?.value) {
                    serviceType = conn.serviceList.ctComServiceList.value;
                } else if (conn.serviceList.cmccServiceList?.value) {
                    serviceType = conn.serviceList.cmccServiceList.value;
                }
            }

            // If no service data from backend, try to determine from connection name
            if (serviceType === 'N/A' && conn.name?.value) {
                const nameLower = conn.name.value.toLowerCase();
                if (nameLower.includes('iptv') || nameLower.includes('tv') || nameLower.includes('multicast')) {
                    serviceType = 'IPTV';
                } else if (nameLower.includes('voip') || nameLower.includes('voice') || nameLower.includes('sip')) {
                    serviceType = 'VOIP';
                } else if (nameLower.includes('tr069') || nameLower.includes('manage') || nameLower.includes('cwmp')) {
                    serviceType = 'TR069';
                } else if (nameLower.includes('internet') || nameLower.includes('wan') || nameLower.includes('pppoe')) {
                    serviceType = 'INTERNET';
                }
            }

            // If still N/A, default to INTERNET
            if (serviceType === 'N/A') {
                serviceType = 'INTERNET';
            }

            // Get actual VLAN value for PPP
            let vlanValue = 'N/A';
            if (conn.vlanInfo) {
                if (conn.vlanInfo.vlanId?.value) {
                    vlanValue = conn.vlanInfo.vlanId.value;
                } else if (conn.vlanInfo.ctComVlanId?.value) {
                    vlanValue = conn.vlanInfo.ctComVlanId.value;
                } else if (conn.vlanInfo.cmccVlanId?.value) {
                    vlanValue = conn.vlanInfo.cmccVlanId.value;
                }
            }

            const tableRow = createPPPParameterRow(
                conn.enable?.value === 'true' || conn.enable?.value === true || conn.enable?.value === 1,
                conn.name?.value || 'N/A',
                vlanValue,
                'PPPoE',
                conn.externalIPAddress?.value || 'N/A',
                conn.username?.value || 'N/A', // Tambah username untuk PPP
                serviceType,
                conn.natEnabled?.value === 'true' || conn.natEnabled?.value === true || conn.natEnabled?.value === 1,
                'PPP_Routed',
                lan1, lan2, lan3, lan4,
                ssid1, ssid2, ssid3, ssid4,
                {
                    type: 'WANPPPConnection',
                    path: conn.path || '',
                    name: conn.name?.value || 'N/A'
                }
            );

            return { tableRow, conn, vlanValue, serviceType, lan1, lan2, lan3, lan4, ssid1, ssid2, ssid3, ssid4 };
        });

        wanPPPRows = pppData.map(item => item.tableRow).join('');

        // Create mobile cards
        wanPPPMobileCards = pppData.map(item => {
            const { conn, vlanValue, serviceType, lan1, lan2, lan3, lan4, ssid1, ssid2, ssid3, ssid4 } = item;
            const lanBindingText = [
                lan1 ? 'LAN1' : null,
                lan2 ? 'LAN2' : null,
                lan3 ? 'LAN3' : null,
                lan4 ? 'LAN4' : null,
                ssid1 ? 'SSID1' : null,
                ssid2 ? 'SSID2' : null,
                ssid3 ? 'SSID3' : null,
                ssid4 ? 'SSID4' : null
            ].filter(Boolean).join(', ') || 'None';

            return `
                <div class="bg-white border border-gray-200 rounded-lg p-4 mb-4 shadow-sm">
                    <div class="flex justify-between items-start mb-3">
                        <h4 class="font-semibold text-gray-900">${conn.name?.value || 'PPP Connection'}</h4>
                        <span class="px-2 py-1 text-xs rounded-full ${(conn.enable?.value === 'true' || conn.enable?.value === true) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${(conn.enable?.value === 'true' || conn.enable?.value === true) ? 'Enabled' : 'Disabled'}
                        </span>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">VLAN:</span>
                            <span class="font-medium">${vlanValue || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Username:</span>
                            <span class="font-medium">${conn.username?.value || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">IP Address:</span>
                            <span class="font-medium">${conn.externalIPAddress?.value || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Service:</span>
                            <span class="font-medium">${serviceType}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">NAT:</span>
                            <span class="font-medium">${(conn.natEnabled?.value === 'true' || conn.natEnabled?.value === true) ? 'Enabled' : 'Disabled'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">LAN Binding:</span>
                            <span class="font-medium">${lanBindingText}</span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="editWANConnection('WANPPPConnection', '${conn.path}')" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Connection
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }


    wanInfoEl.innerHTML = `
        <div class="space-y-8">
            <!-- WAN IP Configuration Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-4 sm:px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                        <div class="flex items-center">
                            <i class="fas fa-ethernet text-blue-600 text-xl mr-3"></i>
                            <div>
                                <h2 class="text-lg sm:text-xl font-semibold text-gray-900">Parameter Setting</h2>
                                <p class="text-sm text-gray-600">WAN IP Configuration</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-ethernet mr-1"></i>
                                ${deviceDetailData.wanConnections.wanIPConnections?.length || 0} IP Connections
                            </span>
                        </div>
                    </div>
                </div>

                <!-- WAN IP Parameter Table -->
                <div class="overflow-x-auto">
                    <!-- Desktop Table View -->
                    <table class="hidden md:table min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enable</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VLAN</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mode</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NAT</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN1</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN2</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN3</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN4</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID1</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID2</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID3</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID4</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${wanIPRows || `
                                <tr>
                                    <td colspan="17" class="px-6 py-8 text-center">
                                        <div class="flex flex-col items-center justify-center">
                                            <i class="fas fa-ethernet text-gray-400 text-3xl mb-4"></i>
                                            <p class="text-gray-500 font-medium">No WAN IP connections configured</p>
                                            <p class="text-gray-400 text-sm">Static IP connections will appear here</p>
                                        </div>
                                    </td>
                                </tr>
                            `}
                        </tbody>
                    </table>

                    <!-- Mobile Card View for WAN IP -->
                    <div class="md:hidden" id="wan-ip-mobile-cards">
                        ${wanIPMobileCards || `
                            <div class="text-center py-8">
                                <i class="fas fa-ethernet text-gray-400 text-3xl mb-4"></i>
                                <p class="text-gray-500 font-medium">No WAN IP connections configured</p>
                                <p class="text-gray-400 text-sm">Static IP connections will appear here</p>
                            </div>
                        `}
                    </div>
                </div>
            </div>

            <!-- WAN PPP Configuration Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-4 sm:px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-user-lock text-blue-600 text-xl mr-3"></i>
                            <div>
                                <h2 class="text-lg sm:text-xl font-semibold text-gray-900">Parameter Setting</h2>
                                <p class="text-sm text-gray-600">WAN PPP Configuration</p>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-user-lock mr-1"></i>
                                ${deviceDetailData.wanConnections.wanPPPConnections?.length || 0} PPP Connections
                            </span>
                            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                <button onclick="showAddWANPPPModal()" class="inline-flex items-center justify-center px-3 py-2 bg-green-600 text-white text-xs font-medium rounded-md hover:bg-green-700 transition-colors">
                                    <i class="fas fa-plus mr-1"></i>
                                    <span class="hidden sm:inline">Add PPP Connection</span>
                                    <span class="sm:hidden">Add</span>
                                </button>
                                <button onclick="summonWANConnections(this)" class="inline-flex items-center justify-center px-3 py-2 bg-purple-600 text-white text-xs font-medium rounded-md hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-sync-alt mr-1"></i>
                                    <span class="hidden sm:inline">Summon WAN</span>
                                    <span class="sm:hidden">Summon</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- WAN PPP Parameter Table -->
                <div class="overflow-x-auto">
                    <!-- Desktop Table View -->
                    <table class="hidden md:table min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enable</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VLAN</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mode</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NAT</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN1</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN2</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN3</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">LAN4</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID1</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID2</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID3</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SSID4</th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${wanPPPRows || `
                                <tr>
                                    <td colspan="18" class="px-6 py-8 text-center">
                                        <div class="flex flex-col items-center justify-center">
                                            <i class="fas fa-user-lock text-gray-400 text-3xl mb-4"></i>
                                            <p class="text-gray-500 font-medium">No WAN PPP connections configured</p>
                                            <p class="text-gray-400 text-sm">PPPoE connections will appear here</p>
                                        </div>
                                    </td>
                                </tr>
                            `}
                        </tbody>
                    </table>

                    <!-- Mobile Card View -->
                    <div class="md:hidden" id="wan-ppp-mobile-cards">
                        ${wanPPPMobileCards || `
                            <div class="text-center py-8">
                                <i class="fas fa-user-lock text-gray-400 text-3xl mb-4"></i>
                                <p class="text-gray-500 font-medium">No WAN PPP connections configured</p>
                                <p class="text-gray-400 text-sm">PPPoE connections will appear here</p>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Function to show Add WAN PPP modal - SIMPLE AddObject Only
function showAddWANPPPModal() {
    const modal = document.createElement('div');
    modal.id = 'addWANPPPModal';
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4';
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl w-full max-w-lg max-h-[95vh] sm:max-h-[90vh] overflow-y-auto mx-2 sm:mx-4">
            <div class="px-4 sm:px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-plus-circle text-purple-600 text-xl mr-3"></i>
                        <h3 class="text-base sm:text-lg font-semibold text-gray-900">Add WAN PPP Connection</h3>
                    </div>
                    <button onclick="closeAddWANPPPModal()" class="text-gray-400 hover:text-gray-600 sm:hidden">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
            </div>
            <div class="px-4 sm:px-6 py-4">
                <div class="space-y-4">
                    <!-- PPP Configuration Form -->
                    <div class="bg-gray-50 border border-gray-200 rounded-md p-3 sm:p-4">
                        <div class="space-y-3 sm:space-y-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2 sm:mb-3">PPP Configuration</h4>

                            <!-- VLAN Input -->
                            <div>
                                <label for="ppp-vlan" class="block text-sm font-medium text-gray-700 mb-1">VLAN ID</label>
                                <input type="number" id="ppp-vlan" name="vlan"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm"
                                       placeholder="Enter VLAN ID (0-4094, 0=untagged)" value="" min="0" max="4094">
                            </div>

                            <!-- Username Input -->
                            <div>
                                <label for="ppp-username" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                                <input type="text" id="ppp-username" name="username"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm"
                                       placeholder="Enter PPP username" value="">
                            </div>

                            <!-- Password Input -->
                            <div>
                                <label for="ppp-password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                                <input type="password" id="ppp-password" name="password"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 text-sm"
                                       placeholder="Enter PPP password" value="">
                            </div>
                        </div>
                    </div>

                    <!-- LAN Binding Configuration -->
                    <div class="bg-gray-50 border border-gray-200 rounded-md p-3 sm:p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2 sm:mb-3">LAN Binding Configuration</h4>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">LAN Ports</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-lan1" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">LAN1</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-lan2" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">LAN2</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-lan3" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">LAN3</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-lan4" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">LAN4</span>
                                    </label>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">WiFi SSIDs</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-ssid1" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">SSID1</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-ssid2" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">SSID2</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-ssid3" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">SSID3</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="add-ssid4" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">SSID4</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 sm:p-4">
                        <div class="flex">
                            <i class="fas fa-check-circle text-yellow-500 mt-0.5 mr-2 sm:mr-3"></i>
                            <div class="text-sm text-yellow-800">
                                <p class="font-medium mb-1">Automatic configuration:</p>
                                <p>The PPP connection will be created, configured with your credentials, LAN binding, and enabled automatically.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-4 sm:px-6 py-4 border-t border-gray-200 flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3">
                <button onclick="closeAddWANPPPModal()" class="w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
                    Cancel
                </button>
                <button onclick="addWANPPPConnection()" class="w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 transition-colors">
                    <i class="fas fa-plus mr-1"></i>
                    Add PPP Connection
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeAddWANPPPModal();
        }
    });

    // Close modal with Escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeAddWANPPPModal();
        }
    };
    document.addEventListener('keydown', handleEscape);

    // Store listener reference for cleanup
    if (!document._addWANPPPEscapeListeners) {
        document._addWANPPPEscapeListeners = [];
    }
    document._addWANPPPEscapeListeners.push(handleEscape);
}

// Function to close Add WAN PPP modal
function closeAddWANPPPModal() {
    // Reset form fields first
    const vlanInput = document.getElementById('ppp-vlan');
    const usernameInput = document.getElementById('ppp-username');
    const passwordInput = document.getElementById('ppp-password');

    if (vlanInput) vlanInput.value = '';
    if (usernameInput) usernameInput.value = '';
    if (passwordInput) passwordInput.value = '';

    // Reset LAN binding checkboxes
    const lanCheckboxes = ['add-lan1', 'add-lan2', 'add-lan3', 'add-lan4', 'add-ssid1', 'add-ssid2', 'add-ssid3', 'add-ssid4'];
    lanCheckboxes.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) checkbox.checked = false;
    });

    // Try multiple selectors to find the modal
    let modal = document.getElementById('addWANPPPModal');
    if (!modal) {
        modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
    }
    if (!modal) {
        // Try to find modal containing the Add WAN PPP form
        modal = document.querySelector('[id*="ppp-vlan"]')?.closest('.fixed.inset-0');
    }

    if (modal) {
        modal.remove();
        console.log('Add WAN PPP modal closed and reset successfully');
    } else {
        console.log('Add WAN PPP modal not found for closing');
        // Force remove any modal that might be stuck
        const allModals = document.querySelectorAll('.fixed.inset-0.bg-black.bg-opacity-50');
        allModals.forEach(m => m.remove());
    }

    // Remove any lingering escape key listeners
    const escapeListeners = document._addWANPPPEscapeListeners || [];
    escapeListeners.forEach(listener => {
        document.removeEventListener('keydown', listener);
    });
    document._addWANPPPEscapeListeners = [];
}



// Function to add WAN PPP connection - WITH Username and Password
async function addWANPPPConnection() {

    const deviceId = getDeviceIdFromURL();

    if (!deviceId) {
        showNotification('Error: Device ID not found', 'error');
        return;
    }

    // Get form inputs
    const vlan = document.getElementById('ppp-vlan').value.trim();
    const username = document.getElementById('ppp-username').value.trim();
    const password = document.getElementById('ppp-password').value.trim();

    // Validate inputs - username and password are required
    if (!username) {
        showNotification('Error: Username is required', 'error');
        return;
    }

    if (!password) {
        showNotification('Error: Password is required', 'error');
        return;
    }

    // Validate VLAN if provided
    if (vlan !== '' && (isNaN(vlan) || parseInt(vlan) < 0 || parseInt(vlan) > 4094)) {
        showNotification('Error: VLAN ID must be between 0 and 4094 (0 = untagged)', 'error');
        return;
    }

    // Get LAN binding inputs
    const lan1 = document.getElementById('add-lan1').checked;
    const lan2 = document.getElementById('add-lan2').checked;
    const lan3 = document.getElementById('add-lan3').checked;
    const lan4 = document.getElementById('add-lan4').checked;
    const ssid1 = document.getElementById('add-ssid1').checked;
    const ssid2 = document.getElementById('add-ssid2').checked;
    const ssid3 = document.getElementById('add-ssid3').checked;
    const ssid4 = document.getElementById('add-ssid4').checked;

    // Show loading state
    const addButton = document.querySelector('button[onclick*="addWANPPPConnection"]');
    const originalText = addButton.innerHTML;
    addButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Adding PPP Connection...';
    addButton.disabled = true;

    try {
        const requestData = {
            deviceId: deviceId,
            username: username,
            password: password
        };

        // Add VLAN if provided (including 0)
        if (vlan !== '') {
            requestData.vlan = parseInt(vlan);
        }

        // Add LAN binding data
        requestData.lanBinding = {
            lan1: lan1,
            lan2: lan2,
            lan3: lan3,
            lan4: lan4,
            ssid1: ssid1,
            ssid2: ssid2,
            ssid3: ssid3,
            ssid4: ssid4
        };

        console.log('Add WAN PPP Debug - Request data:', requestData);
        console.log('Add WAN PPP Debug - LAN binding:', requestData.lanBinding);

        const response = await fetch('SolusiDigital/addwanppp.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });


        // Get raw response text first for debugging
        const responseText = await response.text();

        // Try to parse JSON
        let result;
        try {
            result = JSON.parse(responseText);
        } catch (jsonError) {
            console.error('JSON Parse Error:', jsonError);
            console.error('Raw response that failed to parse:', responseText);
            throw new Error(`Server returned invalid JSON. Status: ${response.status}, Response: ${responseText.substring(0, 200)}`);
        }

        if (result.success) {
            const preservedCount = result.existing_ppp_preserved || 0;

            // Close modal first
            try {
                closeAddWANPPPModal();
            } catch (modalError) {
                console.error('Error closing modal:', modalError);
            }

            // Show success notification
            showNotification(
                `AddObject executed successfully! (${preservedCount} existing PPP connections preserved)`,
                'success'
            );

            // Show path information only if available
            if (result.objectPath) {
                setTimeout(() => {
                    showNotification(
                        `AddObject: ${result.objectPath}`,
                        'info'
                    );
                }, 1500);
            }

            // Show expected result only if available
            if (result.targetPath || result.connection_path) {
                setTimeout(() => {
                    showNotification(
                        `Expected at: ${result.targetPath || result.connection_path}`,
                        'info'
                    );
                }, 3000);
            }

            // Refresh WAN tab data
            setTimeout(() => {
                loadDeviceDetail(deviceId);
            }, 5000); // 5 seconds delay

        } else {
            showNotification('Error: ' + result.error, 'error');

            // Show additional info if available
            if (result.existing_ppp !== undefined) {
                setTimeout(() => {
                    showNotification(
                        `Found ${result.existing_ppp} existing PPP connections. All slots may be occupied.`,
                        'warning'
                    );
                }, 1000);
            }
        }
    } catch (error) {
        console.error('Add WAN PPP Error:', error);
        showNotification('Error: Failed to communicate with server', 'error');
    } finally {
        // Restore button state
        addButton.innerHTML = originalText;
        addButton.disabled = false;
    }
}



// Helper function to get device ID from URL
function getDeviceIdFromURL() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const deviceId = urlParams.get('id');

        // Additional validation
        if (deviceId && deviceId.trim() !== '') {
            return deviceId.trim();
        }
        return null;
    } catch (error) {
        console.log('Error parsing URL parameters:', error);
        return null;
    }
}

// Helper function to show notifications - removed duplicate, using global one

// Update WiFi tab with editable forms
function updateWiFiTab() {
    const wifiInfoEl = document.getElementById('wifi-info');

    // WiFi data structure with correct data from getdetaildevice.php
    const wifiNetworks = [
        {
            id: 'wlan1',
            name: 'SSID 1',
            ssid: deviceDetailData.wifiInfo.wlan1.ssid.value || '',
            security: deviceDetailData.wifiInfo.wlan1.security.value || 'WPAand11i',
            password: '', // Always empty as per requirement
            enabled: deviceDetailData.wifiInfo.wlan1.enabled.value || false,
            stations: deviceDetailData.wifiInfo.wlan1.stations.value || 0,
            channel: deviceDetailData.wifiInfo.wlan1.channel.value || 0,
            isDefault: true // Cannot be deleted
        },
        {
            id: 'wlan2',
            name: 'SSID 2',
            ssid: deviceDetailData.wifiInfo.wlan2.ssid.value || '',
            security: deviceDetailData.wifiInfo.wlan2.security.value || 'WPAand11i',
            password: '', // Always empty as per requirement
            enabled: deviceDetailData.wifiInfo.wlan2.enabled.value || false,
            stations: deviceDetailData.wifiInfo.wlan2.stations.value || 0,
            channel: deviceDetailData.wifiInfo.wlan2.channel.value || 0,
            isDefault: false
        },
        {
            id: 'wlan3',
            name: 'SSID 3',
            ssid: deviceDetailData.wifiInfo.wlan3.ssid.value || '',
            security: deviceDetailData.wifiInfo.wlan3.security.value || 'WPAand11i',
            password: '', // Always empty as per requirement
            enabled: deviceDetailData.wifiInfo.wlan3.enabled.value || false,
            stations: deviceDetailData.wifiInfo.wlan3.stations.value || 0,
            channel: deviceDetailData.wifiInfo.wlan3.channel.value || 0,
            isDefault: false
        },
        {
            id: 'wlan4',
            name: 'SSID 4',
            ssid: deviceDetailData.wifiInfo.wlan4.ssid.value || '',
            security: deviceDetailData.wifiInfo.wlan4.security.value || 'WPAand11i',
            password: '', // Always empty as per requirement
            enabled: deviceDetailData.wifiInfo.wlan4.enabled.value || false,
            stations: deviceDetailData.wifiInfo.wlan4.stations.value || 0,
            channel: deviceDetailData.wifiInfo.wlan4.channel.value || 0,
            isDefault: false
        }
    ];

    const wifiCards = wifiNetworks.map(wifi => {
        const isActive = wifi.enabled === true && wifi.ssid.trim() !== '';

        // Convert security value to readable format
        const getSecurityDisplayValue = (securityValue) => {
            switch(securityValue) {
                case 'None': return 'None';
                case 'WEP': return 'WEP';
                case 'WPA': return 'WPA-PSK';
                case 'WPAand11i': return 'WPA/WPA2-PSK';
                case 'WPA2': return 'WPA2-PSK';
                case 'WPA3': return 'WPA3-PSK';
                default: return securityValue || 'WPA/WPA2-PSK';
            }
        };

        return `
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <!-- Header -->
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                <i class="fas fa-wifi text-blue-600 text-sm sm:text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-base sm:text-lg font-semibold text-gray-900">${wifi.name}</h3>
                                <p class="text-xs sm:text-sm text-gray-600">WiFi Network Configuration</p>
                                ${wifi.stations > 0 ? `<p class="text-xs text-blue-600">${wifi.stations} device(s) connected</p>` : ''}
                            </div>
                        </div>
                        <div class="flex flex-wrap items-center gap-2">
                            <span class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-medium ${isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                <i class="fas ${isActive ? 'fa-check-circle' : 'fa-times-circle'} mr-1"></i>
                                ${isActive ? 'Active' : 'Inactive'}
                            </span>
                            ${wifi.channel > 0 ? `
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Ch ${wifi.channel}
                                </span>
                            ` : ''}
                            ${!wifi.isDefault ? `
                                <button onclick="deleteWiFiNetwork('${wifi.id}')" class="inline-flex items-center px-3 py-1.5 bg-red-500 text-white text-xs font-medium rounded-lg hover:bg-red-600 transition-colors shadow-sm" title="Delete WiFi Network">
                                    <i class="fas fa-trash-alt mr-1.5"></i>
                                    <span class="hidden sm:inline">Delete</span>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="px-4 sm:px-6 py-4 sm:py-6">
                    <div class="space-y-4 sm:space-y-6">
                        <!-- Enable/Disable Toggle -->
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                            <div class="flex-1">
                                <label class="text-sm font-medium text-gray-700">Enable WiFi Network</label>
                                <p class="text-xs text-gray-500 mt-1">Turn this WiFi network on or off</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="enable-${wifi.id}" ${wifi.enabled ? 'checked' : ''} class="sr-only peer" onchange="toggleWiFiNetwork('${wifi.id}')">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <!-- SSID Field with Save Button -->
                        <div class="bg-gray-50 rounded-lg p-3 sm:p-4 border border-gray-200">
                            <label for="ssid-${wifi.id}" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-wifi mr-2 text-blue-600"></i>
                                SSID (Network Name)
                            </label>
                            <div class="space-y-3">
                                <input type="text" id="ssid-${wifi.id}" name="ssid" value="${wifi.ssid}"
                                       class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                       placeholder="Enter WiFi network name">
                                <button type="button" onclick="saveSSID('${wifi.id}')"
                                        id="save-ssid-btn-${wifi.id}"
                                        class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 sm:py-3 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm hover:shadow-md">
                                    <i class="fas fa-save mr-2"></i>
                                    Save SSID
                                </button>
                            </div>
                        </div>

                        <!-- Security Field with Save Button -->
                        <div class="bg-gray-50 rounded-lg p-3 sm:p-4 border border-gray-200">
                            <label for="security-${wifi.id}" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                                Security Type
                                <span class="block sm:inline text-xs text-gray-500 sm:ml-2">(Current: ${getSecurityDisplayValue(wifi.security)})</span>
                            </label>
                            <div class="space-y-3">
                                <input type="text" id="security-${wifi.id}" name="security" value="${wifi.security}"
                                       class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                       placeholder="${wifi.security}">
                                <button type="button" onclick="saveSecurity('${wifi.id}')"
                                        id="save-security-btn-${wifi.id}"
                                        class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 sm:py-3 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 shadow-sm hover:shadow-md">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Security
                                </button>
                            </div>
                        </div>

                        <!-- Password Field with Save Button -->
                        <div class="bg-gray-50 rounded-lg p-3 sm:p-4 border border-gray-200">
                            <label for="password-${wifi.id}" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-key mr-2 text-yellow-600"></i>
                                Password
                                <span class="block sm:inline text-xs text-red-500 sm:ml-2">(Leave empty - will be set separately)</span>
                            </label>
                            <div class="space-y-3">
                                <div class="relative">
                                    <input type="password" id="password-${wifi.id}" name="password" value="${wifi.password}"
                                           class="w-full px-3 sm:px-4 py-2 sm:py-3 pr-10 sm:pr-12 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                           placeholder="Enter new WiFi password">
                                    <button type="button" onclick="togglePasswordVisibility('password-${wifi.id}')"
                                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-eye text-sm" id="eye-password-${wifi.id}"></i>
                                    </button>
                                </div>
                                <button type="button" onclick="savePassword('${wifi.id}')"
                                        id="save-password-btn-${wifi.id}"
                                        class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 sm:py-3 bg-yellow-600 text-white text-sm font-medium rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-all duration-200 shadow-sm hover:shadow-md">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Password
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // Count active networks correctly
    const activeNetworksCount = wifiNetworks.filter(w => w.enabled === true && w.ssid.trim() !== '').length;
    const totalConnectedDevices = wifiNetworks.reduce((total, w) => total + (w.stations || 0), 0);

    wifiInfoEl.innerHTML = `
        <div class="space-y-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div>
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-900">WiFi Networks</h2>
                    <p class="text-gray-600 mt-1">Configure and manage your WiFi networks</p>
                </div>
                <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-wifi mr-1"></i>
                        ${activeNetworksCount} Active Networks
                    </span>
                    ${totalConnectedDevices > 0 ? `
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-users mr-1"></i>
                            ${totalConnectedDevices} Connected Devices
                        </span>
                    ` : ''}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                ${wifiCards}
            </div>
        </div>
    `;
}

// WiFi Management Functions

// Helper function to get WiFi parameter path
function getWiFiPath(wifiId, paramType) {
    if (!deviceDetailData || !deviceDetailData.wifiInfo || !deviceDetailData.wifiInfo[wifiId]) {
        return null;
    }

    const wifiData = deviceDetailData.wifiInfo[wifiId];

    switch (paramType) {
        case 'ssid':
            return wifiData.ssid?.path || null;
        case 'security':
            return wifiData.security?.path || null;
        case 'password':
            return wifiData.password?.path || null;
        case 'enable':
            return wifiData.enabled?.path || null;
        default:
            return null;
    }
}

// Toggle WiFi network enable/disable
async function toggleWiFiNetwork(wifiId) {
    const checkbox = document.getElementById(`enable-${wifiId}`);
    const isEnabled = checkbox.checked;

    // Get enable path from device data
    const enablePath = getWiFiPath(wifiId, 'enable');
    if (!enablePath) {
        showNotification('Error: Could not find enable path for this WiFi network', 'error');
        checkbox.checked = !isEnabled; // Revert
        return;
    }

    try {
        const response = await fetch('SolusiDigital/setwifi.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                deviceId: getDeviceIdFromURL(),
                path: enablePath,
                value: isEnabled,
                dataType: 'xsd:boolean'
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(
                `WiFi ${wifiId.toUpperCase()} ${isEnabled ? 'enabled' : 'disabled'} successfully!`,
                isEnabled ? 'success' : 'info'
            );

            // Don't auto refresh for toggle - let the 30s auto refresh handle it
            // GenieACS might need more time to process the change
        } else {
            // Revert checkbox state on error
            checkbox.checked = !isEnabled;
            showNotification('Error: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('Toggle WiFi Error:', error);
        // Revert checkbox state on error
        checkbox.checked = !isEnabled;
        showNotification('Error: Failed to toggle WiFi network', 'error');
    }
}

// Toggle password visibility
function togglePasswordVisibility(passwordFieldId) {
    const passwordField = document.getElementById(passwordFieldId);
    const eyeIcon = document.getElementById(`eye-${passwordFieldId}`);

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// Save SSID only
async function saveSSID(wifiId) {
    const ssidInput = document.getElementById(`ssid-${wifiId}`);
    const ssidValue = ssidInput.value.trim();

    // Validation
    if (!ssidValue) {
        showNotification('Error: SSID cannot be empty', 'error');
        return;
    }

    // Get SSID path from device data
    const ssidPath = getWiFiPath(wifiId, 'ssid');
    if (!ssidPath) {
        showNotification('Error: Could not find SSID path for this WiFi network', 'error');
        return;
    }

    // Show loading state
    const saveButton = document.getElementById(`save-ssid-btn-${wifiId}`);
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
    saveButton.disabled = true;

    try {

        const response = await fetch('SolusiDigital/setwifi.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                deviceId: getDeviceIdFromURL(),
                path: ssidPath,
                value: ssidValue,
                dataType: 'xsd:string'
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`SSID for ${wifiId.toUpperCase()} saved successfully!`, 'success');
        } else {
            showNotification('Error: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('Save SSID Error:', error);
        showNotification('Error: Failed to save SSID', 'error');
    } finally {
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    }
}

// Save Security only
async function saveSecurity(wifiId) {
    const securityInput = document.getElementById(`security-${wifiId}`);
    const securityValue = securityInput.value.trim();

    // Validation
    if (!securityValue) {
        showNotification('Error: Security type cannot be empty', 'error');
        return;
    }

    // Get security path from device data
    const securityPath = getWiFiPath(wifiId, 'security');
    if (!securityPath) {
        showNotification('Error: Could not find security path for this WiFi network', 'error');
        return;
    }

    // Show loading state
    const saveButton = document.getElementById(`save-security-btn-${wifiId}`);
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
    saveButton.disabled = true;

    try {

        const response = await fetch('SolusiDigital/setwifi.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                deviceId: getDeviceIdFromURL(),
                path: securityPath,
                value: securityValue,
                dataType: 'xsd:string'
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Security type for ${wifiId.toUpperCase()} saved successfully!`, 'success');
        } else {
            showNotification('Error: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('Save Security Error:', error);
        showNotification('Error: Failed to save security type', 'error');
    } finally {
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    }
}

// Save Password only
async function savePassword(wifiId) {
    const passwordInput = document.getElementById(`password-${wifiId}`);
    const passwordValue = passwordInput.value;

    // Validation
    if (!passwordValue) {
        showNotification('Error: Password cannot be empty', 'error');
        return;
    }

    if (passwordValue.length < 8) {
        showNotification('Error: Password must be at least 8 characters long', 'error');
        return;
    }

    // Get password path from device data
    const passwordPath = getWiFiPath(wifiId, 'password');
    if (!passwordPath) {
        showNotification('Error: Could not find password path for this WiFi network', 'error');
        return;
    }

    // Show loading state
    const saveButton = document.getElementById(`save-password-btn-${wifiId}`);
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
    saveButton.disabled = true;

    try {

        const response = await fetch('SolusiDigital/setwifi.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                deviceId: getDeviceIdFromURL(),
                path: passwordPath,
                value: passwordValue,
                dataType: 'xsd:string'
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Password for ${wifiId.toUpperCase()} saved successfully!`, 'success');
        } else {
            showNotification('Error: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('Save Password Error:', error);
        showNotification('Error: Failed to save password', 'error');
    } finally {
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    }
}

// Refresh device detail data
function refreshDeviceDetail() {
    loadDeviceDetail();
}

// Summon device detail parameters
async function summonDeviceDetail() {
    const deviceId = getDeviceIdFromURL();
    if (!deviceId) {
        showNotification('Error: Device ID not found', 'error');
        return;
    }

    // Show loading state on button
    const summonButton = document.getElementById('summon-device');
    const originalText = summonButton.innerHTML;
    summonButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Summoning...';
    summonButton.disabled = true;

    try {

        const response = await fetch('SolusiDigital/summon-detaildevice.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                device_id: deviceId
            })
        });

        const result = await response.json();

        if (result.success) {
            // Show success notification
            if (result.alerts && result.alerts.length > 0) {
                result.alerts.forEach(alert => {
                    showNotification(alert.message, alert.type);
                });
            } else {
                showNotification('Device summoned successfully!', 'success');
            }

            // Reload device data after successful summon
            setTimeout(() => {
                loadDeviceDetail();
            }, 2000); // 2 second delay to allow GenieACS to process
        } else {
            // Show error notifications
            if (result.alerts && result.alerts.length > 0) {
                result.alerts.forEach(alert => {
                    showNotification(alert.message, alert.type);
                });
            } else {
                showNotification('Failed to summon device', 'error');
            }
        }

    } catch (error) {
        console.error('Summon device error:', error);
        showNotification('Error: Failed to summon device', 'error');
    } finally {
        // Restore button state
        summonButton.innerHTML = originalText;
        summonButton.disabled = false;
    }
}

// Delete WiFi network
function deleteWiFiNetwork(wifiId) {
    // Show confirmation dialog
    showConfirmDialog(
        'Delete WiFi Network',
        `Are you sure you want to delete ${wifiId.toUpperCase()}? This action cannot be undone.`,
        async function() {
            // User confirmed deletion
            try {

                // Simulate API call - replace with actual API endpoint
                await new Promise(resolve => setTimeout(resolve, 1000));

                showNotification(`WiFi ${wifiId.toUpperCase()} deleted successfully!`, 'success');

                // Refresh WiFi tab
                setTimeout(() => {
                    updateWiFiTab();
                }, 1000);

                // TODO: Replace with actual API call
                // const response = await fetch('SolusiDigital/delete-wifi.php', {
                //     method: 'POST',
                //     headers: {
                //         'Content-Type': 'application/json',
                //     },
                //     body: JSON.stringify({
                //         deviceId: getDeviceIdFromURL(),
                //         wifiId: wifiId
                //     })
                // });

                // const result = await response.json();
                // if (result.success) {
                //     showNotification(`WiFi ${wifiId.toUpperCase()} deleted successfully!`, 'success');
                //     updateWiFiTab(); // Refresh the tab
                // } else {
                //     showNotification('Error: ' + result.error, 'error');
                // }

            } catch (error) {
                console.error('Delete WiFi Error:', error);
                showNotification('Error: Failed to delete WiFi network', 'error');
            }
        },
        function() {
            // User cancelled deletion
        }
    );
}

// Update system tab
function updateSystemTab() {
    // Update Basic Information section (from overview)
    const basicInfoEl = document.getElementById('basic-info');
    const connectionInfoEl = document.getElementById('connection-info');

    // Basic Information
    basicInfoEl.innerHTML = `
        <div class="bg-gray-50 rounded-lg p-4 space-y-3">
            <div class="flex flex-col sm:flex-row sm:justify-between">
                <span class="text-sm font-medium text-gray-600">Device ID:</span>
                <span class="text-sm text-gray-900 font-mono break-all">${deviceDetailData._id || 'N/A'}</span>
            </div>
            <div class="flex flex-col sm:flex-row sm:justify-between">
                <span class="text-sm font-medium text-gray-600">Product Class:</span>
                <span class="text-sm text-gray-900">${deviceDetailData.deviceInfo.productclass || 'N/A'}</span>
            </div>
            <div class="flex flex-col sm:flex-row sm:justify-between">
                <span class="text-sm font-medium text-gray-600">PPPoE Username:</span>
                <span class="text-sm text-gray-900 break-all">${getPPPoEUsernameFromConnections() || 'N/A'}</span>
            </div>
            <div class="flex flex-col sm:flex-row sm:justify-between">
                <span class="text-sm font-medium text-gray-600">Serial Number:</span>
                <span class="text-sm text-gray-900">${deviceDetailData.deviceInfo.serialNumber || 'N/A'}</span>
            </div>
        </div>
    `;

    // Connection Information
    const statusClass = getStatusClass(deviceDetailData.connectionInfo._lastInform);
    connectionInfoEl.innerHTML = `
        <div class="bg-gray-50 rounded-lg p-4 space-y-3">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <span class="text-sm font-medium text-gray-600">Status:</span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusClass.bg} ${statusClass.text} mt-1 sm:mt-0">
                    <i class="fas ${statusClass.icon} mr-1"></i>
                    ${statusClass.label}
                </span>
            </div>
            <div class="flex flex-col sm:flex-row sm:justify-between">
                <span class="text-sm font-medium text-gray-600">RX Power:</span>
                <span class="text-sm text-gray-900">${deviceDetailData.monitoringInfo.rxpower.value ? deviceDetailData.monitoringInfo.rxpower.value + ' dBm' : 'N/A'}</span>
            </div>
            <div class="flex flex-col sm:flex-row sm:justify-between">
                <span class="text-sm font-medium text-gray-600">Temperature:</span>
                <span class="text-sm text-gray-900">${deviceDetailData.monitoringInfo.temperature.value || 'N/A'}</span>
            </div>
            <div class="flex flex-col sm:flex-row sm:justify-between">
                <span class="text-sm font-medium text-gray-600">Active Devices:</span>
                <span class="text-sm text-gray-900">${deviceDetailData.monitoringInfo.activedevices.value || '0'}</span>
            </div>
        </div>
    `;

    // System specific information (additional system details)
    const systemInfoEl = document.getElementById('system-info');
    const systemSpecificContent = systemInfoEl.querySelector('.system-specific-content');

    if (systemSpecificContent) {
        systemSpecificContent.innerHTML = `
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">System Information</h3>
                    <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div class="flex flex-col sm:flex-row sm:justify-between">
                            <span class="text-sm font-medium text-gray-600">Manufacturer:</span>
                            <span class="text-sm text-gray-900">${deviceDetailData.deviceInfo.manufacturer || 'N/A'}</span>
                        </div>
                        <div class="flex flex-col sm:flex-row sm:justify-between">
                            <span class="text-sm font-medium text-gray-600">Hardware Version:</span>
                            <span class="text-sm text-gray-900">${deviceDetailData.deviceInfo.hardwareVersion || 'N/A'}</span>
                        </div>
                        <div class="flex flex-col sm:flex-row sm:justify-between">
                            <span class="text-sm font-medium text-gray-600">Software Version:</span>
                            <span class="text-sm text-gray-900">${deviceDetailData.deviceInfo.softwareVersion || 'N/A'}</span>
                        </div>
                        <div class="flex flex-col sm:flex-row sm:justify-between">
                            <span class="text-sm font-medium text-gray-600">Last Inform:</span>
                            <span class="text-sm text-gray-900">${formatLastInform(deviceDetailData.connectionInfo._lastInform)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

// Tab switching
function switchTab(tabName) {
    // Update active tab button
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });
    
    const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active', 'border-blue-500', 'text-blue-600');
        activeBtn.classList.remove('border-transparent', 'text-gray-500');
    }
    
    // Hide all tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(`tab-${tabName}`);
    if (selectedTab) {
        selectedTab.classList.remove('hidden');
    }
    
    currentDetailTab = tabName;

    // Update content for the selected tab
    if (deviceDetailData) {
        updateTabContent();
    }
}

// Action functions
function goBack() {
    window.location.href = 'index.php?page=device';
}

function refreshDeviceDetail() {
    const refreshBtn = document.getElementById('refresh-device');
    const originalText = refreshBtn.innerHTML;
    
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...';
    refreshBtn.disabled = true;
    
    loadDeviceDetail();
    
    setTimeout(() => {
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
    }, 1000);
}



// Helper function to get PPPoE username from WAN connections
function getPPPoEUsernameFromConnections() {
    if (deviceDetailData && deviceDetailData.wanConnections && deviceDetailData.wanConnections.wanPPPConnections) {
        const pppConnections = deviceDetailData.wanConnections.wanPPPConnections;
        for (let conn of pppConnections) {
            if (conn.username?.value && conn.username.value !== 'N/A') {
                return conn.username.value;
            }
        }
    }
    return null;
}

// Utility functions (reuse from device.js)
function getStatusClass(lastInform) {
    if (!lastInform) {
        return {
            bg: 'bg-gray-100',
            text: 'text-gray-800',
            border: 'border-gray-200',
            icon: 'fa-question-circle',
            label: 'Unknown'
        };
    }
    
    const now = new Date();
    const lastInformDate = new Date(lastInform);
    const diffMinutes = (now - lastInformDate) / (1000 * 60);
    
    if (diffMinutes <= 5) {
        return {
            bg: 'bg-green-100',
            text: 'text-green-800',
            border: 'border-green-200',
            icon: 'fa-check-circle',
            label: 'Online'
        };
    } else if (diffMinutes <= 30) {
        return {
            bg: 'bg-yellow-100',
            text: 'text-yellow-800',
            border: 'border-yellow-200',
            icon: 'fa-exclamation-triangle',
            label: 'Warning'
        };
    } else {
        return {
            bg: 'bg-red-100',
            text: 'text-red-800',
            border: 'border-red-200',
            icon: 'fa-times-circle',
            label: 'Offline'
        };
    }
}

function getRxPowerClass(rxpower) {
    if (!rxpower || isNaN(rxpower)) {
        return {
            bg: 'bg-gray-100',
            text: 'text-gray-600',
            border: 'border-gray-200',
            label: 'Unknown'
        };
    }
    
    if (rxpower >= -20) {
        return {
            bg: 'bg-green-100',
            text: 'text-green-600',
            border: 'border-green-200',
            label: 'Excellent'
        };
    } else if (rxpower >= -25) {
        return {
            bg: 'bg-yellow-100',
            text: 'text-yellow-600',
            border: 'border-yellow-200',
            label: 'Fair'
        };
    } else {
        return {
            bg: 'bg-red-100',
            text: 'text-red-600',
            border: 'border-red-200',
            label: 'Poor'
        };
    }
}

function formatLastInform(lastInform) {
    if (!lastInform) return 'Never';
    
    const now = new Date();
    const lastInformDate = new Date(lastInform);
    const diffMs = now - lastInformDate;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMinutes < 1) {
        return 'Just now';
    } else if (diffMinutes < 60) {
        return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
        return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
        return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    }
}

function showError(message) {
    console.error(message);
    showNotification(message, 'error');
}

function showNotification(message, type = 'info') {
    // Use the global notification system
    if (window.GenieACS && window.GenieACS.showNotification) {
        console.log('Using main.js notification system:', message, type);
        window.GenieACS.showNotification(message, type);
    } else {
        console.log('GenieACS not available, using fallback notification');
        // Create a simple fallback notification
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-3 rounded-md shadow-lg z-50 ${
            type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
            type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
            type === 'warning' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
            'bg-blue-100 text-blue-800 border border-blue-200'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                } mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Function to edit WAN connection
function editWANConnection(connectionType, connectionPath) {

    // Find the connection data
    let connectionData = null;
    if (connectionType === 'WANIPConnection' && deviceDetailData.wanConnections.wanIPConnections) {
        connectionData = deviceDetailData.wanConnections.wanIPConnections.find(conn => conn.path === connectionPath);
    } else if (connectionType === 'WANPPPConnection' && deviceDetailData.wanConnections.wanPPPConnections) {
        connectionData = deviceDetailData.wanConnections.wanPPPConnections.find(conn => conn.path === connectionPath);
    }

    if (!connectionData) {
        showError('Connection data not found');
        return;
    }

    // Show edit modal based on connection type
    if (connectionType === 'WANPPPConnection') {
        showEditWANPPPModal(connectionData);
    } else {
        showEditWANIPModal(connectionData);
    }
}

// Function to show edit WAN PPP modal
function showEditWANPPPModal(connectionData) {
    // Get current values
    const currentVlan = connectionData.vlanInfo?.vlanId?.value ||
                       connectionData.vlanInfo?.ctComVlanId?.value ||
                       connectionData.vlanInfo?.cmccVlanId?.value || '';
    const currentUsername = connectionData.username?.value || '';
    const currentName = connectionData.name?.value || '';

    // Get LAN binding current values
    let lan1 = false, lan2 = false, lan3 = false, lan4 = false;
    let ssid1 = false, ssid2 = false, ssid3 = false, ssid4 = false;

    if (connectionData.lanBinding) {
        if (connectionData.lanBinding.type === 'huawei') {
            lan1 = connectionData.lanBinding.lan1Enable?.value === 1 || connectionData.lanBinding.lan1Enable?.value === '1';
            lan2 = connectionData.lanBinding.lan2Enable?.value === 1 || connectionData.lanBinding.lan2Enable?.value === '1';
            lan3 = connectionData.lanBinding.lan3Enable?.value === 1 || connectionData.lanBinding.lan3Enable?.value === '1';
            lan4 = connectionData.lanBinding.lan4Enable?.value === 1 || connectionData.lanBinding.lan4Enable?.value === '1';
            ssid1 = connectionData.lanBinding.ssid1Enable?.value === 1 || connectionData.lanBinding.ssid1Enable?.value === '1';
            ssid2 = connectionData.lanBinding.ssid2Enable?.value === 1 || connectionData.lanBinding.ssid2Enable?.value === '1';
            ssid3 = connectionData.lanBinding.ssid3Enable?.value === 1 || connectionData.lanBinding.ssid3Enable?.value === '1';
            ssid4 = connectionData.lanBinding.ssid4Enable?.value === 1 || connectionData.lanBinding.ssid4Enable?.value === '1';
        } else if (connectionData.lanBinding.type === 'zte') {
            const interfaceString = connectionData.lanBinding.ctComInterface?.value || connectionData.lanBinding.cmccInterface?.value || '';
            const interfaces = interfaceString.split(',');
            lan1 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.1'));
            lan2 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.2'));
            lan3 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.3'));
            lan4 = interfaces.some(iface => iface.includes('LANEthernetInterfaceConfig.4'));
            ssid1 = interfaces.some(iface => iface.includes('WLANConfiguration.1'));
            ssid2 = interfaces.some(iface => iface.includes('WLANConfiguration.2'));
            ssid3 = interfaces.some(iface => iface.includes('WLANConfiguration.3'));
            ssid4 = interfaces.some(iface => iface.includes('WLANConfiguration.4'));
        }
    }

    // Create modal HTML
    const modalHTML = `
        <div id="editWANPPPModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 p-2 sm:p-4">
            <div class="relative top-2 sm:top-4 lg:top-10 mx-auto p-3 sm:p-4 lg:p-5 border w-full max-w-[95vw] sm:max-w-[600px] shadow-lg rounded-md bg-white max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
                <div class="mt-2 sm:mt-3">
                    <div class="flex items-center justify-between mb-3 sm:mb-4">
                        <h3 class="text-base sm:text-lg font-medium text-gray-900">Edit WAN PPP Connection</h3>
                        <button onclick="closeEditWANPPPModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>

                    <!-- Connection Name (Read-only) -->
                    <div class="mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Connection Name</label>
                        <input type="text" value="${currentName}" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600 cursor-not-allowed text-sm">
                        <p class="text-xs text-gray-500 mt-1">Connection name cannot be changed</p>
                    </div>

                    <!-- VLAN Form -->
                    <div class="mb-4 sm:mb-6 p-3 sm:p-4 border border-gray-200 rounded-lg">
                        <h4 class="text-sm sm:text-md font-medium text-gray-900 mb-3">VLAN Configuration</h4>
                        <form id="editVLANForm" class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">VLAN ID</label>
                                <input type="number" id="editPPPVlan" value="${currentVlan}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            </div>
                            <div class="flex justify-end">
                                <button type="submit"
                                        class="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-save mr-1"></i>Save VLAN
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Username Form -->
                    <div class="mb-4 sm:mb-6 p-3 sm:p-4 border border-gray-200 rounded-lg">
                        <h4 class="text-sm sm:text-md font-medium text-gray-900 mb-3">Username Configuration</h4>
                        <form id="editUsernameForm" class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                                <input type="text" id="editPPPUsername" value="${currentUsername}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            </div>
                            <div class="flex justify-end">
                                <button type="submit"
                                        class="w-full sm:w-auto px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors">
                                    <i class="fas fa-save mr-1"></i>Save Username
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Password Form -->
                    <div class="mb-4 sm:mb-6 p-3 sm:p-4 border border-gray-200 rounded-lg">
                        <h4 class="text-sm sm:text-md font-medium text-gray-900 mb-3">Password Configuration</h4>
                        <form id="editPasswordForm" class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                                <input type="password" id="editPPPPassword" placeholder="Enter new password"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            </div>
                            <div class="flex justify-end">
                                <button type="submit"
                                        class="w-full sm:w-auto px-4 py-2 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-save mr-1"></i>Save Password
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- LAN Binding Form -->
                    <div class="mb-4 sm:mb-6 p-3 sm:p-4 border border-gray-200 rounded-lg">
                        <h4 class="text-sm sm:text-md font-medium text-gray-900 mb-3">LAN Binding Configuration</h4>
                        <form id="editLANBindingForm" class="space-y-4">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">LAN Ports</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="lan1" ${lan1 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">LAN1</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="lan2" ${lan2 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">LAN2</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="lan3" ${lan3 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">LAN3</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="lan4" ${lan4 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">LAN4</span>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">WiFi SSIDs</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="ssid1" ${ssid1 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">SSID1</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="ssid2" ${ssid2 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">SSID2</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="ssid3" ${ssid3 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">SSID3</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="ssid4" ${ssid4 ? 'checked' : ''} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">SSID4</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button type="submit"
                                        class="w-full sm:w-auto px-4 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors">
                                    <i class="fas fa-save mr-1"></i>Save LAN Binding
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="flex justify-end pt-3 sm:pt-4 border-t border-gray-200">
                        <button onclick="closeEditWANPPPModal()"
                                class="w-full sm:w-auto px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add form submit handlers
    document.getElementById('editVLANForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveVLANChanges(connectionData);
    });

    document.getElementById('editUsernameForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveUsernameChanges(connectionData);
    });

    document.getElementById('editPasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        savePasswordChanges(connectionData);
    });

    document.getElementById('editLANBindingForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveLANBindingChanges(connectionData);
    });
}

// Function to close edit WAN PPP modal
function closeEditWANPPPModal() {
    const modal = document.getElementById('editWANPPPModal');
    if (modal) {
        // Add fade out animation
        modal.style.opacity = '0';
        modal.style.transition = 'opacity 0.3s ease-out';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Function to save VLAN changes
async function saveVLANChanges(connectionData) {
    const vlan = document.getElementById('editPPPVlan').value.trim();
    const saveButton = document.querySelector('#editVLANForm button[type="submit"]');

    if (!vlan) {
        showError('VLAN ID is required');
        return;
    }

    // Show loading state
    const originalText = saveButton.innerHTML;
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Saving...';

    try {
        const basePath = connectionData.path;
        let vlanPath = '';

        // Determine VLAN path based on device type and current data structure
        const deviceInfo = deviceDetailData.deviceInfo;
        if (deviceInfo.manufacturer?.toLowerCase().includes('huawei')) {
            vlanPath = basePath + '.X_HW_VLAN'; // FIXED: X_HW_VLAN not X_HW_VLANID
        } else if (deviceInfo.manufacturer?.toLowerCase().includes('zte')) {
            // For ZTE, VLAN is at WANConnectionDevice level, not connection level
            // Extract WANConnectionDevice path from basePath
            const wanConnDeviceMatch = basePath.match(/(InternetGatewayDevice\.WANDevice\.\d+\.WANConnectionDevice\.\d+)/);
            if (wanConnDeviceMatch) {
                const wanConnDevicePath = wanConnDeviceMatch[1];
                // Check which VLAN path exists in current data
                if (connectionData.vlanInfo?.ctComVlanId) {
                    vlanPath = wanConnDevicePath + '.X_CT-COM_WANEponLinkConfig.VLANIDMark';
                } else if (connectionData.vlanInfo?.cmccVlanId) {
                    vlanPath = wanConnDevicePath + '.X_CMCC_WANEponLinkConfig.VLANIDMark';
                } else {
                    vlanPath = wanConnDevicePath + '.X_CT-COM_WANEponLinkConfig.VLANIDMark'; // Default for ZTE
                }
            } else {
                vlanPath = basePath + '.X_CT-COM_VLANID'; // Fallback
            }
        } else {
            vlanPath = basePath + '.X_HW_VLAN'; // Default fallback - FIXED
        }

        const response = await fetch('SolusiDigital/editwan.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                deviceId: deviceDetailData._id,
                path: vlanPath,
                value: vlan,
                dataType: 'xsd:unsignedInt'
            })
        });

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || 'Failed to update VLAN');
        }

        showNotification('VLAN updated successfully', 'success');

        // Reload device data
        setTimeout(() => {
            loadDeviceDetail();
        }, 2000);

    } catch (error) {
        console.error('Error updating VLAN:', error);
        showError('Failed to update VLAN: ' + error.message);
    } finally {
        // Reset loading state
        saveButton.disabled = false;
        saveButton.innerHTML = originalText;
    }
}

// Function to save username changes
async function saveUsernameChanges(connectionData) {
    const username = document.getElementById('editPPPUsername').value.trim();
    const saveButton = document.querySelector('#editUsernameForm button[type="submit"]');

    if (!username) {
        showError('Username is required');
        return;
    }

    // Show loading state
    const originalText = saveButton.innerHTML;
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Saving...';

    try {
        const basePath = connectionData.path;
        const usernamePath = basePath + '.Username';

        const response = await fetch('SolusiDigital/editwan.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                deviceId: deviceDetailData._id,
                path: usernamePath,
                value: username,
                dataType: 'xsd:string'
            })
        });

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || 'Failed to update username');
        }

        showNotification('Username updated successfully', 'success');

        // Reload device data
        setTimeout(() => {
            loadDeviceDetail();
        }, 2000);

    } catch (error) {
        console.error('Error updating username:', error);
        showError('Failed to update username: ' + error.message);
    } finally {
        // Reset loading state
        saveButton.disabled = false;
        saveButton.innerHTML = originalText;
    }
}

// Function to save password changes
async function savePasswordChanges(connectionData) {
    const password = document.getElementById('editPPPPassword').value.trim();
    const saveButton = document.querySelector('#editPasswordForm button[type="submit"]');

    if (!password) {
        showError('Password is required');
        return;
    }

    // Show loading state
    const originalText = saveButton.innerHTML;
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Saving...';

    try {
        const basePath = connectionData.path;
        const passwordPath = basePath + '.Password';

        const response = await fetch('SolusiDigital/editwan.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                deviceId: deviceDetailData._id,
                path: passwordPath,
                value: password,
                dataType: 'xsd:string'
            })
        });

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || 'Failed to update password');
        }

        showNotification('Password updated successfully', 'success');

        // Clear password field
        document.getElementById('editPPPPassword').value = '';

    } catch (error) {
        console.error('Error updating password:', error);
        showError('Failed to update password: ' + error.message);
    } finally {
        // Reset loading state
        saveButton.disabled = false;
        saveButton.innerHTML = originalText;
    }
}

// Function to save LAN binding changes
async function saveLANBindingChanges(connectionData) {
    const saveButton = document.querySelector('#editLANBindingForm button[type="submit"]');

    // Show loading state
    const originalText = saveButton.innerHTML;
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Saving...';

    try {
        const basePath = connectionData.path;

        // Get checkbox values
        const lan1 = document.getElementById('lan1').checked;
        const lan2 = document.getElementById('lan2').checked;
        const lan3 = document.getElementById('lan3').checked;
        const lan4 = document.getElementById('lan4').checked;
        const ssid1 = document.getElementById('ssid1').checked;
        const ssid2 = document.getElementById('ssid2').checked;
        const ssid3 = document.getElementById('ssid3').checked;
        const ssid4 = document.getElementById('ssid4').checked;

        // Use lanBinding.type from connection data to determine device type
        const deviceType = connectionData.lanBinding?.type;
        console.log('LAN Binding Debug - Device type:', deviceType);
        console.log('LAN Binding Debug - Connection data:', connectionData);
        console.log('LAN Binding Debug - Base path:', basePath);

        if (deviceType === 'huawei') {
            // For Huawei, update individual enable flags
            const updates = [
                { path: basePath + '.X_HW_LANBIND.Lan1Enable', value: lan1 ? '1' : '0', dataType: 'xsd:unsignedInt' },
                { path: basePath + '.X_HW_LANBIND.Lan2Enable', value: lan2 ? '1' : '0', dataType: 'xsd:unsignedInt' },
                { path: basePath + '.X_HW_LANBIND.Lan3Enable', value: lan3 ? '1' : '0', dataType: 'xsd:unsignedInt' },
                { path: basePath + '.X_HW_LANBIND.Lan4Enable', value: lan4 ? '1' : '0', dataType: 'xsd:unsignedInt' },
                { path: basePath + '.X_HW_LANBIND.SSID1Enable', value: ssid1 ? '1' : '0', dataType: 'xsd:unsignedInt' },
                { path: basePath + '.X_HW_LANBIND.SSID2Enable', value: ssid2 ? '1' : '0', dataType: 'xsd:unsignedInt' },
                { path: basePath + '.X_HW_LANBIND.SSID3Enable', value: ssid3 ? '1' : '0', dataType: 'xsd:unsignedInt' },
                { path: basePath + '.X_HW_LANBIND.SSID4Enable', value: ssid4 ? '1' : '0', dataType: 'xsd:unsignedInt' }
            ];

            // Send updates one by one
            for (const update of updates) {
                const response = await fetch('SolusiDigital/editwan.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        deviceId: deviceDetailData._id,
                        path: update.path,
                        value: update.value,
                        dataType: update.dataType
                    })
                });

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || 'Failed to update LAN binding');
                }
            }

        } else if (deviceType === 'zte') {
            // For ZTE, build interface string
            const interfaces = [];

            // Add LAN interfaces
            if (lan1) interfaces.push('InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1');
            if (lan2) interfaces.push('InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.2');
            if (lan3) interfaces.push('InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.3');
            if (lan4) interfaces.push('InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.4');

            // Add WLAN interfaces
            if (ssid1) interfaces.push('InternetGatewayDevice.LANDevice.1.WLANConfiguration.1');
            if (ssid2) interfaces.push('InternetGatewayDevice.LANDevice.1.WLANConfiguration.2');
            if (ssid3) interfaces.push('InternetGatewayDevice.LANDevice.1.WLANConfiguration.3');
            if (ssid4) interfaces.push('InternetGatewayDevice.LANDevice.1.WLANConfiguration.4');

            const interfaceString = interfaces.join(',');

            // Determine which interface path to use based on current data
            let interfacePath = '';
            if (connectionData.lanBinding?.ctComInterface) {
                interfacePath = basePath + '.X_CT-COM_LanInterface';
            } else if (connectionData.lanBinding?.cmccInterface) {
                interfacePath = basePath + '.X_CMCC_LanInterface';
            } else {
                interfacePath = basePath + '.X_CT-COM_LanInterface'; // Default for ZTE
            }

            const response = await fetch('SolusiDigital/editwan.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    deviceId: deviceDetailData._id,
                    path: interfacePath,
                    value: interfaceString,
                    dataType: 'xsd:string'
                })
            });

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error || 'Failed to update LAN binding');
            }
        } else {
            throw new Error(`Unsupported device type for LAN binding. Device type: "${deviceType || 'Unknown'}". Expected 'huawei' or 'zte'.`);
        }

        showNotification('LAN binding updated successfully', 'success');

        // Reload device data
        setTimeout(() => {
            loadDeviceDetail();
        }, 2000);

    } catch (error) {
        console.error('Error updating LAN binding:', error);
        showError('Failed to update LAN binding: ' + error.message);
    } finally {
        // Reset loading state
        saveButton.disabled = false;
        saveButton.innerHTML = originalText;
    }
}

// Function to show edit WAN IP modal (placeholder for now)
function showEditWANIPModal(connectionData) {
    showNotification('WAN IP connection editing will be implemented soon', 'info');
}

// Function to create mobile card view for tables
function createMobileCard(data, type) {
    const cardClass = "bg-white border border-gray-200 rounded-lg p-4 mb-4 shadow-sm";

    // Auto-detect type if not provided
    if (!type) {
        if (data.connectionTypeForEdit === 'WANIPConnection') {
            type = 'wan-ip';
        } else if (data.connectionTypeForEdit === 'WANPPPConnection') {
            type = 'wan-ppp';
        }
    }

    if (type === 'wan-ppp') {
        return `
            <div class="${cardClass}">
                <div class="flex justify-between items-start mb-3">
                    <h4 class="font-semibold text-gray-900">${data.name || 'PPP Connection'}</h4>
                    <span class="px-2 py-1 text-xs rounded-full ${data.connectionStatus === 'Connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${data.connectionStatus || 'Unknown'}
                    </span>
                </div>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">VLAN:</span>
                        <span class="font-medium">${data.vlan || 'N/A'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Username:</span>
                        <span class="font-medium">${data.username || 'N/A'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">External IP:</span>
                        <span class="font-medium">${data.externalIPAddress || 'N/A'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Default Gateway:</span>
                        <span class="font-medium">${data.defaultGateway || 'N/A'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">DNS Servers:</span>
                        <span class="font-medium">${data.dnsServers || 'N/A'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">LAN Binding:</span>
                        <span class="font-medium">${data.lanBinding || 'N/A'}</span>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="editWANConnection('WANPPPConnection', '${data.path}')" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Connection
                    </button>
                </div>
            </div>
        `;
    }

    if (type === 'wan-ip') {
        return `
            <div class="${cardClass}">
                <div class="flex justify-between items-start mb-3">
                    <div>
                        <h4 class="font-semibold text-gray-900">${data.name || 'IP Connection'}</h4>
                        <p class="text-sm text-gray-500">${data.service || 'INTERNET'}</p>
                    </div>
                    <span class="px-2 py-1 text-xs rounded-full ${data.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${data.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                </div>

                <div class="grid grid-cols-2 gap-3 text-sm mb-4">
                    <div>
                        <span class="text-gray-600">VLAN:</span>
                        <p class="font-medium">${data.vlan || 'N/A'}</p>
                    </div>
                    <div>
                        <span class="text-gray-600">Type:</span>
                        <p class="font-medium">${data.type || 'DHCP'}</p>
                    </div>
                    <div>
                        <span class="text-gray-600">IP Address:</span>
                        <p class="font-medium">${data.ip || 'N/A'}</p>
                    </div>
                    <div>
                        <span class="text-gray-600">NAT:</span>
                        <p class="font-medium">${data.nat || 'Disabled'}</p>
                    </div>
                    <div class="col-span-2">
                        <span class="text-gray-600">LAN Binding:</span>
                        <p class="font-medium">${data.lanBinding || 'None'}</p>
                    </div>
                </div>

                <div class="mt-4">
                    <button onclick="editWANConnection('WANIPConnection', '${data.path}')" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Connection
                    </button>
                </div>
            </div>
        `;
    }

    return '';
}

// Function to summon WAN connections (refresh WAN data)
async function summonWANConnections(buttonElement) {
    // Find button element
    const button = buttonElement || document.querySelector('button[onclick="summonWANConnections()"]');
    const originalContent = '<i class="fas fa-sync-alt mr-1"></i>Summon WAN';

    try {
        // Show loading animation
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Summoning...';
        }

        showNotification('Summoning WAN connections...', 'info');

        const deviceId = getDeviceIdFromURL();
        if (!deviceId) {
            showNotification('Device ID not found', 'error');
            return;
        }

        const response = await fetch('SolusiDigital/summon-wan.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                deviceId: deviceId
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`WAN connections summoned successfully! (${result.parameters_summoned} parameters)`, 'success');
            // Refresh device detail data without page reload (like other summon functions)
            setTimeout(() => {
                loadDeviceDetail(); // This will update the WAN data
            }, 1000);
        } else {
            showNotification(`Failed to summon WAN connections: ${result.message}`, 'error');
        }

    } catch (error) {
        console.error('Error summoning WAN connections:', error);
        showNotification('Error summoning WAN connections', 'error');
    } finally {
        // Restore button state
        if (button) {
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    }
}
