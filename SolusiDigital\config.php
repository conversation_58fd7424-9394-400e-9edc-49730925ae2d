<?php
/**
 * Configuration file for GenieACS API integration
 * SolusiDigital - GenieACS Panel
 */

// GenieACS API Configuration
define('GENIEACS_URL', 'https://sempakmamel.apiku.my.id/devices');

// API Endpoints
define('GENIEACS_DEVICES_ENDPOINT', '/devices');
define('GENIEACS_TASKS_ENDPOINT', '/tasks');
define('GENIEACS_PRESETS_ENDPOINT', '/presets');
define('GENIEACS_PROVISIONS_ENDPOINT', '/provisions');


// API Settings
define('API_TIMEOUT', 30); // seconds
define('API_USER_AGENT', 'SolusiDigital-GenieACS-Panel/1.0');

// Response Settings
define('DEFAULT_RESPONSE_FORMAT', 'json');
define('MAX_DEVICES_PER_PAGE', 100);

// Error Codes
define('ERROR_API_CONNECTION', 1001);
define('ERROR_API_TIMEOUT', 1002);
define('ERROR_INVALID_RESPONSE', 1003);
define('ERROR_DEVICE_NOT_FOUND', 1004);
define('ERROR_UNAUTHORIZED', 1005);

// Success Codes
define('SUCCESS_OK', 200);
define('SUCCESS_CREATED', 201);
define('SUCCESS_NO_CONTENT', 204);

/**
 * Get GenieACS API URL with endpoint
 * @param string $endpoint
 * @return string
 */
function getGenieACSUrl($endpoint = '') {
    return GENIEACS_BASE_URL . $endpoint;
}

/**
 * Get API headers
 * @return array
 */
function getApiHeaders() {
    return [
        'Content-Type: application/json',
        'User-Agent: ' . API_USER_AGENT,
        'Accept: application/json'
    ];
}

/**
 * Log API requests (for debugging)
 * @param string $message
 * @param string $level
 */
function logApiRequest($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    // Write to log file (create logs directory if needed)
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logDir . '/api.log', $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Validate API response
 * @param mixed $response
 * @param int $httpCode
 * @return bool
 */
function validateApiResponse($response, $httpCode) {
    if ($httpCode >= 200 && $httpCode < 300) {
        return true;
    }
    return false;
}

/**
 * Format error response
 * @param string $message
 * @param int $code
 * @param array $details
 * @return array
 */
function formatErrorResponse($message, $code, $details = []) {
    return [
        'success' => false,
        'error' => [
            'message' => $message,
            'code' => $code,
            'details' => $details
        ],
        'timestamp' => date('c')
    ];
}

/**
 * Format success response
 * @param mixed $data
 * @param string $message
 * @return array
 */
function formatSuccessResponse($data, $message = 'Success') {
    return [
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('c')
    ];
}

// Set timezone
date_default_timezone_set('Asia/Jakarta');

// Enable error reporting for development
if (defined('DEVELOPMENT') && DEVELOPMENT) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
