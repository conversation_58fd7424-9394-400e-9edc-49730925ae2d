<?php
// Ambil URL API dari config.php
require_once 'config.php';

$baseUrl = GENIEACS_URL;

// Get device ID from parameter - keep it exactly as received
$deviceId = isset($_GET['id']) ? $_GET['id'] : null;

if (!$deviceId) {
    http_response_code(400);
    echo json_encode(['error' => 'Device ID parameter is required']);
    exit;
}

// Projection yang lebih spesifik untuk menghindari metadata yang tidak perlu
// Kita ambil parameter WAN yang spesifik saja untuk menghindari _object, _writable, _timestamp
$projection = [
    '_id',
    '_deviceId._ProductClass',
    '_deviceId._SerialNumber',
    '_deviceId._Manufacturer',
    '_deviceId._OUI',
    'VirtualParameters.pppoeUsername',
    'VirtualParameters.pppoeUsername2',
    'VirtualParameters.RXPower',
    'VirtualParameters.gettemp',
    'VirtualParameters.activedevices',
    // WiFi parameters - spesifik untuk menghindari metadata
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.PreSharedKey.1.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.TotalAssociations',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.PreSharedKey.1.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.TotalAssociations',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.PreSharedKey.1.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.TotalAssociations',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.Channel',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.Enable',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.PreSharedKey.1.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.KeyPassphrase',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.BeaconType',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.TotalAssociations',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.Channel',
    // Device Info
    'InternetGatewayDevice.DeviceInfo.HardwareVersion',
    'InternetGatewayDevice.DeviceInfo.SoftwareVersion',
    'InternetGatewayDevice.DeviceInfo.UpTime',
    // MAC Address
    'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress',
    'InternetGatewayDevice.WANDevice.1.WANEthernetInterfaceConfig.MACAddress',
    // Ambil seluruh struktur WAN tapi kita akan filter metadata di processing
    // Ini lebih efisien daripada list semua parameter satu per satu
    'InternetGatewayDevice.WANDevice',
    '_lastInform',
    '_lastBoot',
    '_registered'
];

// Build query with exact ID as received
$query = json_encode(['_id' => $deviceId]);
$apiUrl = $baseUrl . '?query=' . urlencode($query) . '&projection=' . urlencode(implode(',', $projection));

// Ambil data dari API GenieACS
$ch = curl_init($apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
curl_setopt($ch, CURLOPT_ENCODING, 'gzip');
$result = curl_exec($ch);

if (curl_errno($ch)) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch API', 'detail' => curl_error($ch)]);
    curl_close($ch);
    exit;
}
curl_close($ch);

$data = json_decode($result, true);
if (!is_array($data)) {
    http_response_code(500);
    echo json_encode(['error' => 'Invalid API response']);
    exit;
}



if (!$baseUrl) {
    http_response_code(500);
    echo json_encode(['error' => 'API URL not set in config.php']);
    exit;
}

// Check if device found
if (empty($data)) {
    http_response_code(404);
    echo json_encode([
        'error' => 'Device not found in detail query',
        'deviceId' => $deviceId
    ]);
    exit;
}

// Process device detail data
$item = $data[0]; // Ambil device pertama (seharusnya hanya 1)
$vp = isset($item['VirtualParameters']) ? $item['VirtualParameters'] : [];
// PPPoE Username sekarang diambil langsung dari parameter TR-069 di fungsi getPPPUsername()
// Tidak lagi menggunakan virtual parameter pppoeUsername dan pppoeUsername2
// $pppsecret juga tidak diperlukan lagi karena username diambil per-connection
$rxpower = isset($vp['RXPower']) ? $vp['RXPower']['_value'] : null;
$gettemp = isset($vp['gettemp']) ? $vp['gettemp']['_value'] : null;
$activedevices = isset($vp['activedevices']) ? $vp['activedevices']['_value'] : null;
$_lastInformUTC = isset($item['_lastInform']) ? $item['_lastInform'] : null;
$_lastBootUTC = isset($item['_lastBoot']) ? $item['_lastBoot'] : null;
$_registered = isset($item['_registered']) ? $item['_registered'] : null;

// Device ID info
$deviceIdInfo = isset($item['_deviceId']) ? $item['_deviceId'] : [];
$typeont = isset($deviceIdInfo['_ProductClass']) ? $deviceIdInfo['_ProductClass'] : null;
$serialNumber = isset($deviceIdInfo['_SerialNumber']) ? $deviceIdInfo['_SerialNumber'] : null;
$manufacturer = isset($deviceIdInfo['_Manufacturer']) ? $deviceIdInfo['_Manufacturer'] : null;
$oui = isset($deviceIdInfo['_OUI']) ? $deviceIdInfo['_OUI'] : null;

// Detect vendor based on manufacturer or product class
$vendor = 'unknown';
if ($manufacturer) {
    $manufacturerLower = strtolower($manufacturer);
    if (strpos($manufacturerLower, 'huawei') !== false) {
        $vendor = 'huawei';
    } elseif (strpos($manufacturerLower, 'zte') !== false || strpos($manufacturerLower, 'zicg') !== false) {
        $vendor = 'zte';
    }
}

// If manufacturer detection failed, try product class
if ($vendor === 'unknown' && $typeont) {
    $productClassLower = strtolower($typeont);
    if (strpos($productClassLower, 'eg8') !== false || strpos($productClassLower, 'hg8') !== false) {
        $vendor = 'huawei';
    } elseif (strpos($productClassLower, 'zx') !== false || strpos($productClassLower, 'f6') !== false) {
        $vendor = 'zte';
    }
}

// Device Info
$hardwareVersion = isset($item['InternetGatewayDevice']['DeviceInfo']['HardwareVersion']['_value']) ? $item['InternetGatewayDevice']['DeviceInfo']['HardwareVersion']['_value'] : null;
$softwareVersion = isset($item['InternetGatewayDevice']['DeviceInfo']['SoftwareVersion']['_value']) ? $item['InternetGatewayDevice']['DeviceInfo']['SoftwareVersion']['_value'] : null;
$upTime = isset($item['InternetGatewayDevice']['DeviceInfo']['UpTime']['_value']) ? $item['InternetGatewayDevice']['DeviceInfo']['UpTime']['_value'] : null;

// Device MAC Address - try multiple possible locations
$deviceMacAddress = null;
if (isset($item['InternetGatewayDevice']['LANDevice']['1']['LANEthernetInterfaceConfig']['1']['MACAddress']['_value'])) {
    $deviceMacAddress = $item['InternetGatewayDevice']['LANDevice']['1']['LANEthernetInterfaceConfig']['1']['MACAddress']['_value'];
} elseif (isset($item['InternetGatewayDevice']['WANDevice']['1']['WANEthernetInterfaceConfig']['MACAddress']['_value'])) {
    $deviceMacAddress = $item['InternetGatewayDevice']['WANDevice']['1']['WANEthernetInterfaceConfig']['MACAddress']['_value'];
}

// Helper function untuk membersihkan metadata GenieACS
function cleanGenieACSData($data) {
    if (!is_array($data)) {
        return $data;
    }

    $cleaned = [];
    foreach ($data as $key => $value) {
        // Skip metadata GenieACS
        if (in_array($key, ['_object', '_writable', '_timestamp', '_type', '_instance'])) {
            continue;
        }

        if (is_array($value)) {
            $cleaned[$key] = cleanGenieACSData($value);
        } else {
            $cleaned[$key] = $value;
        }
    }

    return $cleaned;
}

// WAN Info - Process all WAN connections
$wanConnections = [];
$wanDevices = isset($item['InternetGatewayDevice']['WANDevice']) ? $item['InternetGatewayDevice']['WANDevice'] : [];

// Clean metadata dari WAN devices sebelum processing
$wanDevices = cleanGenieACSData($wanDevices);

foreach ($wanDevices as $wanDeviceIndex => $wanDevice) {
    if (isset($wanDevice['WANConnectionDevice'])) {
        foreach ($wanDevice['WANConnectionDevice'] as $connDeviceIndex => $connDevice) {

            // Check for WANIPConnection
            if (isset($connDevice['WANIPConnection'])) {
                foreach ($connDevice['WANIPConnection'] as $ipConnIndex => $ipConn) {
                    // Base path untuk connection ini
                    $basePath = "InternetGatewayDevice.WANDevice.{$wanDeviceIndex}.WANConnectionDevice.{$connDeviceIndex}.WANIPConnection.{$ipConnIndex}";
                    
                    // Filter hanya data yang penting, skip metadata internal
                    $connectionData = [
                        'type' => 'WANIPConnection',
                        'path' => $basePath,
                        'wanDeviceIndex' => $wanDeviceIndex,
                        'connDeviceIndex' => $connDeviceIndex,
                        'index' => $ipConnIndex,
                        'enable' => [
                            'path' => "{$basePath}.Enable",
                            'value' => isset($ipConn['Enable']['_value']) ? $ipConn['Enable']['_value'] : null
                        ],
                        'connectionStatus' => [
                            'path' => "{$basePath}.ConnectionStatus",
                            'value' => isset($ipConn['ConnectionStatus']['_value']) ? $ipConn['ConnectionStatus']['_value'] : null
                        ],
                        'externalIPAddress' => [
                            'path' => "{$basePath}.ExternalIPAddress",
                            'value' => isset($ipConn['ExternalIPAddress']['_value']) ? $ipConn['ExternalIPAddress']['_value'] : null
                        ],
                        'subnetMask' => [
                            'path' => "{$basePath}.SubnetMask",
                            'value' => isset($ipConn['SubnetMask']['_value']) ? $ipConn['SubnetMask']['_value'] : null
                        ],
                        'defaultGateway' => [
                            'path' => "{$basePath}.DefaultGateway",
                            'value' => isset($ipConn['DefaultGateway']['_value']) ? $ipConn['DefaultGateway']['_value'] : null
                        ],
                        'dnsServers' => [
                            'path' => "{$basePath}.DNSServers",
                            'value' => isset($ipConn['DNSServers']['_value']) ? $ipConn['DNSServers']['_value'] : null
                        ],
                        'connectionType' => [
                            'path' => "{$basePath}.ConnectionType",
                            'value' => isset($ipConn['ConnectionType']['_value']) ? $ipConn['ConnectionType']['_value'] : null
                        ],
                        'name' => [
                            'path' => "{$basePath}.Name",
                            'value' => isset($ipConn['Name']['_value']) ? $ipConn['Name']['_value'] : null
                        ],
                        'natEnabled' => [
                            'path' => "{$basePath}.NATEnabled",
                            'value' => isset($ipConn['NATEnabled']['_value']) ? $ipConn['NATEnabled']['_value'] : null
                        ],
                        'addressingType' => [
                            'path' => "{$basePath}.AddressingType",
                            'value' => isset($ipConn['AddressingType']['_value']) ? $ipConn['AddressingType']['_value'] : null
                        ],
                        'serviceList' => createServiceList($vendor, $ipConn, $basePath), // Tambah service list
                        'lanBinding' => createLanBinding($vendor, $ipConn, $basePath),
                        'vlanInfo' => createVlanInfo($vendor, $ipConn, $connDevice, $wanDeviceIndex, $connDeviceIndex, $basePath)
                    ];

                    // Tambahkan semua connections yang ditemukan untuk debugging
                    // Bahkan yang tidak memiliki data lengkap, untuk melihat struktur
                    $wanConnections[] = $connectionData;
                }
            }

            // Check for WANPPPConnection
            if (isset($connDevice['WANPPPConnection'])) {
                foreach ($connDevice['WANPPPConnection'] as $pppConnIndex => $pppConn) {
                    // Base path untuk connection ini
                    $basePath = "InternetGatewayDevice.WANDevice.{$wanDeviceIndex}.WANConnectionDevice.{$connDeviceIndex}.WANPPPConnection.{$pppConnIndex}";
                    
                    // Filter hanya data yang penting, skip metadata internal
                    $connectionData = [
                        'type' => 'WANPPPConnection',
                        'path' => $basePath,
                        'wanDeviceIndex' => $wanDeviceIndex,
                        'connDeviceIndex' => $connDeviceIndex,
                        'index' => $pppConnIndex,
                        'enable' => [
                            'path' => "{$basePath}.Enable",
                            'value' => isset($pppConn['Enable']['_value']) ? $pppConn['Enable']['_value'] : null
                        ],
                        'connectionStatus' => [
                            'path' => "{$basePath}.ConnectionStatus",
                            'value' => isset($pppConn['ConnectionStatus']['_value']) ? $pppConn['ConnectionStatus']['_value'] : null
                        ],
                        'externalIPAddress' => [
                            'path' => "{$basePath}.ExternalIPAddress",
                            'value' => isset($pppConn['ExternalIPAddress']['_value']) ? $pppConn['ExternalIPAddress']['_value'] : null
                        ],
                        'username' => [
                            'path' => "{$basePath}.Username",
                            'value' => getPPPUsername($item, $wanDeviceIndex, $connDeviceIndex, $pppConnIndex)
                        ],
                        'dnsServers' => [
                            'path' => "{$basePath}.DNSServers",
                            'value' => isset($pppConn['DNSServers']['_value']) ? $pppConn['DNSServers']['_value'] : null
                        ],
                        'connectionType' => [
                            'path' => "{$basePath}.ConnectionType",
                            'value' => isset($pppConn['ConnectionType']['_value']) ? $pppConn['ConnectionType']['_value'] : null
                        ],
                        'name' => [
                            'path' => "{$basePath}.Name",
                            'value' => isset($pppConn['Name']['_value']) ? $pppConn['Name']['_value'] : null
                        ],
                        'natEnabled' => [
                            'path' => "{$basePath}.NATEnabled",
                            'value' => isset($pppConn['NATEnabled']['_value']) ? $pppConn['NATEnabled']['_value'] : null
                        ],
                        'lastConnectionError' => [
                            'path' => "{$basePath}.LastConnectionError",
                            'value' => isset($pppConn['LastConnectionError']['_value']) ? $pppConn['LastConnectionError']['_value'] : null
                        ],
                        'serviceList' => createServiceList($vendor, $pppConn, $basePath), // Tambah service list
                        'lanBinding' => createLanBinding($vendor, $pppConn, $basePath),
                        'vlanInfo' => createVlanInfo($vendor, $pppConn, $connDevice, $wanDeviceIndex, $connDeviceIndex, $basePath)
                    ];

                    // Tambahkan semua connections yang ditemukan untuk debugging
                    // Bahkan yang tidak memiliki data lengkap, untuk melihat struktur
                    $wanConnections[] = $connectionData;
                }
            }
        }
    }
}

// Helper function untuk membuat LAN Binding yang bersih
function createLanBinding($vendor, $connection, $basePath = '') {
    if ($vendor === 'huawei' && isset($connection['X_HW_LANBIND'])) {
        $lanBind = $connection['X_HW_LANBIND'];
        return [
            'type' => 'huawei',
            'lan1Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.Lan1Enable",
                'value' => isset($lanBind['Lan1Enable']['_value']) ? $lanBind['Lan1Enable']['_value'] : null
            ],
            'lan2Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.Lan2Enable",
                'value' => isset($lanBind['Lan2Enable']['_value']) ? $lanBind['Lan2Enable']['_value'] : null
            ],
            'lan3Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.Lan3Enable",
                'value' => isset($lanBind['Lan3Enable']['_value']) ? $lanBind['Lan3Enable']['_value'] : null
            ],
            'lan4Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.Lan4Enable",
                'value' => isset($lanBind['Lan4Enable']['_value']) ? $lanBind['Lan4Enable']['_value'] : null
            ],
            'ssid1Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.SSID1Enable",
                'value' => isset($lanBind['SSID1Enable']['_value']) ? $lanBind['SSID1Enable']['_value'] : null
            ],
            'ssid2Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.SSID2Enable",
                'value' => isset($lanBind['SSID2Enable']['_value']) ? $lanBind['SSID2Enable']['_value'] : null
            ],
            'ssid3Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.SSID3Enable",
                'value' => isset($lanBind['SSID3Enable']['_value']) ? $lanBind['SSID3Enable']['_value'] : null
            ],
            'ssid4Enable' => [
                'path' => "{$basePath}.X_HW_LANBIND.SSID4Enable",
                'value' => isset($lanBind['SSID4Enable']['_value']) ? $lanBind['SSID4Enable']['_value'] : null
            ]
        ];
    } elseif ($vendor === 'zte') {
        $lanData = [];

        $zteCTInterface = isset($connection['X_CT-COM_LanInterface']['_value']) ? $connection['X_CT-COM_LanInterface']['_value'] : null;
        $zteCMCCInterface = isset($connection['X_CMCC_LanInterface']['_value']) ? $connection['X_CMCC_LanInterface']['_value'] : null;

        if ($zteCTInterface !== null && $zteCTInterface !== '') {
            $lanData['ctComInterface'] = [
                'path' => "{$basePath}.X_CT-COM_LanInterface",
                'value' => $zteCTInterface
            ];
        }

        if ($zteCMCCInterface !== null && $zteCMCCInterface !== '') {
            $lanData['cmccInterface'] = [
                'path' => "{$basePath}.X_CMCC_LanInterface",
                'value' => $zteCMCCInterface
            ];
        }

        if (!empty($lanData)) {
            return array_merge(['type' => 'zte'], $lanData);
        }
    }

    return null;
}

// Helper function untuk membuat VLAN Info dengan struktur yang benar
function createVlanInfo($vendor, $connection, $connDevice = null, $wanDeviceIndex = 1, $connDeviceIndex = 1, $basePath = '') {
    if ($vendor === 'huawei') {
        // Huawei: VLAN ada di dalam connection itu sendiri
        // InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANIPConnection.1.X_HW_VLAN
        // InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.1.X_HW_VLAN
        $vlanValue = isset($connection['X_HW_VLAN']['_value']) ? $connection['X_HW_VLAN']['_value'] : null;

        if ($vlanValue !== null && $vlanValue !== '' && $vlanValue !== 0) {
            return [
                'type' => 'huawei',
                'vlanId' => [
                    'path' => "{$basePath}.X_HW_VLAN",
                    'value' => $vlanValue
                ]
            ];
        }
    } elseif ($vendor === 'zte') {
        // ZTE: VLAN ada di level WANConnectionDevice, bukan di dalam connection
        // InternetGatewayDevice.WANDevice.1.WANConnectionDevice.2.X_CT-COM_WANEponLinkConfig.VLANIDMark
        // InternetGatewayDevice.WANDevice.1.WANConnectionDevice.2.X_CMCC_WANEponLinkConfig.VLANIDMark
        $vlanData = [];

        // Check X_CT-COM_WANEponLinkConfig.VLANIDMark di level WANConnectionDevice
        if ($connDevice && isset($connDevice['X_CT-COM_WANEponLinkConfig']['VLANIDMark']['_value'])) {
            $zteCTVlan = $connDevice['X_CT-COM_WANEponLinkConfig']['VLANIDMark']['_value'];
            if ($zteCTVlan !== null && $zteCTVlan !== '' && $zteCTVlan !== 0) {
                $vlanData['ctComVlanId'] = [
                    'path' => "InternetGatewayDevice.WANDevice.{$wanDeviceIndex}.WANConnectionDevice.{$connDeviceIndex}.X_CT-COM_WANEponLinkConfig.VLANIDMark",
                    'value' => $zteCTVlan
                ];
            }
        }

        // Check X_CMCC_WANEponLinkConfig.VLANIDMark di level WANConnectionDevice
        if ($connDevice && isset($connDevice['X_CMCC_WANEponLinkConfig']['VLANIDMark']['_value'])) {
            $zteCMCCVlan = $connDevice['X_CMCC_WANEponLinkConfig']['VLANIDMark']['_value'];
            if ($zteCMCCVlan !== null && $zteCMCCVlan !== '' && $zteCMCCVlan !== 0) {
                $vlanData['cmccVlanId'] = [
                    'path' => "InternetGatewayDevice.WANDevice.{$wanDeviceIndex}.WANConnectionDevice.{$connDeviceIndex}.X_CMCC_WANEponLinkConfig.VLANIDMark",
                    'value' => $zteCMCCVlan
                ];
            }
        }

        // Juga check parameter VLAN yang mungkin ada di connection level (fallback)
        if (isset($connection['X_CMCC_VLANIDMark']['_value'])) {
            $connLevelVlan = $connection['X_CMCC_VLANIDMark']['_value'];
            if ($connLevelVlan !== null && $connLevelVlan !== '' && $connLevelVlan !== 0) {
                $vlanData['connectionLevelVlan'] = [
                    'path' => "{$basePath}.X_CMCC_VLANIDMark",
                    'value' => $connLevelVlan
                ];
            }
        }

        if (!empty($vlanData)) {
            return array_merge(['type' => 'zte'], $vlanData);
        }
    }

    return null;
}

// Helper function untuk membuat Service List
function createServiceList($vendor, $connection, $basePath = '') {
    $serviceData = [];

    if ($vendor === 'huawei') {
        // Huawei menggunakan X_HW_ServiceList
        if (isset($connection['X_HW_ServiceList']['_value'])) {
            $serviceList = $connection['X_HW_ServiceList']['_value'];
            $serviceData = [
                'type' => 'huawei',
                'serviceList' => [
                    'path' => "{$basePath}.X_HW_ServiceList",
                    'value' => $serviceList
                ]
            ];
        }
    } elseif ($vendor === 'zte') {
        // ZTE menggunakan X_CT-COM_ServiceList atau X_CMCC_ServiceList
        if (isset($connection['X_CT-COM_ServiceList']['_value'])) {
            $serviceList = $connection['X_CT-COM_ServiceList']['_value'];
            $serviceData = [
                'type' => 'zte',
                'ctComServiceList' => [
                    'path' => "{$basePath}.X_CT-COM_ServiceList",
                    'value' => $serviceList
                ]
            ];
        } elseif (isset($connection['X_CMCC_ServiceList']['_value'])) {
            $serviceList = $connection['X_CMCC_ServiceList']['_value'];
            $serviceData = [
                'type' => 'zte',
                'cmccServiceList' => [
                    'path' => "{$basePath}.X_CMCC_ServiceList",
                    'value' => $serviceList
                ]
            ];
        }
    }

    return !empty($serviceData) ? $serviceData : null;
}

// Helper function untuk mengambil PPP Username dari parameter TR-069
function getPPPUsername($item, $wanDeviceIndex, $connDeviceIndex, $pppConnIndex) {
    // Path parameter: InternetGatewayDevice.WANDevice.1.WANConnectionDevice.2.WANPPPConnection.1.Username
    $parameterPath = "InternetGatewayDevice.WANDevice.{$wanDeviceIndex}.WANConnectionDevice.{$connDeviceIndex}.WANPPPConnection.{$pppConnIndex}.Username";


    // Cari parameter di data device
    if (isset($item[$parameterPath]['_value'])) {
        $username = $item[$parameterPath]['_value'];
        return $username;
    }

    // Fallback: coba ambil dari struktur nested jika ada
    if (isset($item['InternetGatewayDevice']['WANDevice'][$wanDeviceIndex]['WANConnectionDevice'][$connDeviceIndex]['WANPPPConnection'][$pppConnIndex]['Username']['_value'])) {
        $username = $item['InternetGatewayDevice']['WANDevice'][$wanDeviceIndex]['WANConnectionDevice'][$connDeviceIndex]['WANPPPConnection'][$pppConnIndex]['Username']['_value'];
        return $username;
    }

    return null;
}

// Group WAN connections by type
$wanIPConnections = array_filter($wanConnections, function($conn) { return $conn['type'] === 'WANIPConnection'; });
$wanPPPConnections = array_filter($wanConnections, function($conn) { return $conn['type'] === 'WANPPPConnection'; });

// Ambil WiFi Configuration 1-4 dengan parameter berbeda berdasarkan vendor
$wifiConfigurations = [];
for ($i = 1; $i <= 4; $i++) {
    $wlanConfig = isset($item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration'][$i]) ? $item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration'][$i] : [];

    $enabled = isset($wlanConfig['Enable']['_value']) ? $wlanConfig['Enable']['_value'] : null;
    $ssidValue = isset($wlanConfig['SSID']['_value']) ? $wlanConfig['SSID']['_value'] : null;
    $security = isset($wlanConfig['BeaconType']['_value']) ? $wlanConfig['BeaconType']['_value'] : null;
    $stations = isset($wlanConfig['TotalAssociations']['_value']) ? $wlanConfig['TotalAssociations']['_value'] : null;
    $channel = isset($wlanConfig['Channel']['_value']) ? $wlanConfig['Channel']['_value'] : null;

    // Password path berbeda berdasarkan vendor
    $password = null;
    $passwordPath = '';

    if ($vendor === 'huawei') {
        // Huawei menggunakan PreSharedKey.1.KeyPassphrase
        $password = isset($wlanConfig['PreSharedKey']['1']['KeyPassphrase']['_value']) ? $wlanConfig['PreSharedKey']['1']['KeyPassphrase']['_value'] : null;
        $passwordPath = "InternetGatewayDevice.LANDevice.1.WLANConfiguration.{$i}.PreSharedKey.1.KeyPassphrase";
    } else {
        // Non-Huawei (ZTE, dll) menggunakan KeyPassphrase langsung
        $password = isset($wlanConfig['KeyPassphrase']['_value']) ? $wlanConfig['KeyPassphrase']['_value'] : null;
        $passwordPath = "InternetGatewayDevice.LANDevice.1.WLANConfiguration.{$i}.KeyPassphrase";
    }

    $wifiConfigurations["wlan{$i}"] = [
        'enabled' => [
            'path' => "InternetGatewayDevice.LANDevice.1.WLANConfiguration.{$i}.Enable",
            'value' => $enabled
        ],
        'ssid' => [
            'path' => "InternetGatewayDevice.LANDevice.1.WLANConfiguration.{$i}.SSID",
            'value' => $ssidValue
        ],
        'password' => [
            'path' => $passwordPath,
            'value' => $password
        ],
        'security' => [
            'path' => "InternetGatewayDevice.LANDevice.1.WLANConfiguration.{$i}.BeaconType",
            'value' => $security
        ],
        'stations' => [
            'path' => "InternetGatewayDevice.LANDevice.1.WLANConfiguration.{$i}.TotalAssociations",
            'value' => $stations
        ],
        'channel' => [
            'path' => "InternetGatewayDevice.LANDevice.1.WLANConfiguration.{$i}.Channel",
            'value' => $channel
        ]
    ];
}



// Format timestamps ke WIB (Asia/Jakarta)
$_lastInformWIB = null;
if ($_lastInformUTC) {
    try {
        $dt = new DateTime($_lastInformUTC, new DateTimeZone('UTC'));
        $dt->setTimezone(new DateTimeZone('Asia/Jakarta'));
        $_lastInformWIB = $dt->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        $_lastInformWIB = 'Invalid date';
    }
}

$_lastBootWIB = null;
if ($_lastBootUTC) {
    try {
        $dt = new DateTime($_lastBootUTC, new DateTimeZone('UTC'));
        $dt->setTimezone(new DateTimeZone('Asia/Jakarta'));
        $_lastBootWIB = $dt->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        $_lastBootWIB = 'Invalid date';
    }
}

$_registeredWIB = null;
if ($_registered) {
    try {
        $dt = new DateTime($_registered, new DateTimeZone('UTC'));
        $dt->setTimezone(new DateTimeZone('Asia/Jakarta'));
        $_registeredWIB = $dt->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        $_registeredWIB = 'Invalid date';
    }
}

// Build detailed output
$output = [
    '_id' => $deviceId,
    'vendor' => $vendor,
    'deviceInfo' => [
        'productclass' => $typeont,
        'serialNumber' => $serialNumber,
        'manufacturer' => $manufacturer,
        'oui' => $oui,
        'hardwareVersion' => $hardwareVersion,
        'softwareVersion' => $softwareVersion,
        'upTime' => $upTime,
        'macAddress' => $deviceMacAddress
    ],
    'connectionInfo' => [
        // PPPoE username sekarang ada di setiap WAN PPP connection individual
        '_lastInform' => $_lastInformWIB,
        '_lastBoot' => $_lastBootWIB,
        '_registered' => $_registeredWIB
    ],
    'wanConnections' => [
        'wanIPConnections' => array_values($wanIPConnections),
        'wanPPPConnections' => array_values($wanPPPConnections),
        'totalConnections' => count($wanConnections),
        'totalIPConnections' => count($wanIPConnections),
        'totalPPPConnections' => count($wanPPPConnections),
        // Debug info untuk melihat struktur WAN yang ditemukan
        'debugInfo' => [
            'wanDevicesFound' => array_keys($wanDevices),
            'vendor' => $vendor,
            'wanDevicesCount' => count($wanDevices)
        ]
    ],
    'wifiInfo' => [
        'wlan1' => $wifiConfigurations['wlan1'],
        'wlan2' => $wifiConfigurations['wlan2'],
        'wlan3' => $wifiConfigurations['wlan3'],
        'wlan4' => $wifiConfigurations['wlan4']
    ],
    'monitoringInfo' => [
        'rxpower' => [
            'path' => 'VirtualParameters.RXPower',
            'value' => $rxpower
        ],
        'temperature' => [
            'path' => 'VirtualParameters.gettemp',
            'value' => $gettemp
        ],
        'activedevices' => [
            'path' => 'VirtualParameters.activedevices',
            'value' => $activedevices
        ]
    ]
];

// Tampilkan ke browser
header('Content-Type: application/json');
echo json_encode($output, JSON_PRETTY_PRINT);
?>
