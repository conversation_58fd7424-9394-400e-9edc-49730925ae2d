<!-- //device -->
<div class="fade-in">
    <!-- RX Power Stats -->
    <div class="stats-grid grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Excellent Signal</p>
                    <p class="text-2xl font-bold text-green-600" id="excellent-signal"></p>
                    <p class="text-xs text-green-600 mt-1">devices</p>
                </div>
                <div class="p-3 bg-green-100 rounded-xl">
                    <i class="fas fa-signal text-green-600 text-xl"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Fair Signal</p>
                    <p class="text-2xl font-bold text-yellow-600" id="fair-signal"></p>
                    <p class="text-xs text-yellow-600 mt-1">device</p>
                </div>
                <div class="p-3 bg-yellow-100 rounded-xl">
                    <i class="fas fa-signal text-yellow-600 text-xl"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Poor Signal</p>
                    <p class="text-2xl font-bold text-red-600" id="poor-signal"></p>
                    <p class="text-xs text-red-600 mt-1">device</p>
                </div>
                <div class="p-3 bg-red-100 rounded-xl">
                    <i class="fas fa-signal text-red-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Device Management -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h3 class="text-lg font-semibold text-gray-900">Device Management</h3>
            <div class="search-container relative w-full sm:w-80">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" id="device-search" placeholder="Search devices by ProductClass, Device ID, SSID..." class="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" onkeyup="searchDevices(this.value)">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button id="clear-search" onclick="clearSearch()" class="text-gray-400 hover:text-gray-600 hidden">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Desktop Table View -->
        <div class="hidden md:block table-container overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="device-table">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ProductClass</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rxpower</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PPPoE</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SSID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WanBridge</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Inform</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="device-table-body">
                    <!-- Data akan dimuat via AJAX -->
                    <tr>
                        <td colspan="9" class="px-6 py-8 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                                <p class="text-gray-500">Loading devices...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Mobile Card View -->
        <div class="md:hidden" id="device-mobile-cards">
            <div class="space-y-4 p-4">
                <div class="flex flex-col items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                    <p class="text-gray-500">Loading devices...</p>
                </div>
            </div>
        </div>
        
        <!-- Pagination -->
        <div class="pagination-container px-6 py-4 border-t border-gray-200 flex items-center justify-between" id="pagination-container">
            <div class="text-sm text-gray-500">
                Showing <span id="showing-from">0</span> to <span id="showing-to">0</span> of <span id="total-devices-count">0</span> devices
            </div>
            <div class="pagination-buttons flex space-x-2" id="pagination-buttons">
                <!-- Pagination buttons akan dimuat via JavaScript -->
            </div>
        </div>
    </div>
</div>


