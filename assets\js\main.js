// Main JavaScript file for GenieACS Panel
// Contains global functions and utilities

// Global variables
let sidebarOpen = false;

// Sidebar toggle functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobile-overlay');

    // For mobile devices
    if (window.innerWidth < 1024) {
        if (sidebar.classList.contains('-translate-x-full')) {
            sidebar.classList.remove('-translate-x-full');
            sidebar.classList.add('translate-x-0', 'show');
            overlay.classList.remove('hidden');
            sidebarOpen = true;
        } else {
            sidebar.classList.add('-translate-x-full');
            sidebar.classList.remove('translate-x-0', 'show');
            overlay.classList.add('hidden');
            sidebarOpen = false;
        }
    }
}

function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobile-overlay');

    if (window.innerWidth < 1024) {
        sidebar.classList.add('-translate-x-full');
        sidebar.classList.remove('translate-x-0', 'show');
        overlay.classList.add('hidden');
        sidebarOpen = false;
    }
}

// Toggle switch utility function
function toggleSwitch(button) {
    const span = button.querySelector('span');
    
    if (button.classList.contains('bg-blue-600')) {
        button.classList.remove('bg-blue-600');
        button.classList.add('bg-gray-200');
        span.classList.remove('translate-x-6');
        span.classList.add('translate-x-1');
        return false; // Switch is OFF
    } else {
        button.classList.add('bg-blue-600');
        button.classList.remove('bg-gray-200');
        span.classList.add('translate-x-6');
        span.classList.remove('translate-x-1');
        return true; // Switch is ON
    }
}

// Alert System - Centralized alert management
function initializeAlertSystem() {
    // Create alert container if it doesn't exist
    if (!document.getElementById('alert-container')) {
        const alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
        alertContainer.style.maxWidth = '400px';
        document.body.appendChild(alertContainer);
    }
}

// Show Success Alert
function showSuccessAlert(title, message, duration = 5000) {
    const alertHtml = `
        <div class="alert-item bg-green-50 border border-green-200 rounded-lg p-4 shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-green-800">${title}</h3>
                    <p class="text-sm text-green-700 mt-1">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="close-alert text-green-400 hover:text-green-600 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    showAlert(alertHtml, duration);
}

// Show Error Alert
function showErrorAlert(title, message, duration = 7000) {
    const alertHtml = `
        <div class="alert-item bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-600 text-xl"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-red-800">${title}</h3>
                    <p class="text-sm text-red-700 mt-1">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="close-alert text-red-400 hover:text-red-600 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    showAlert(alertHtml, duration);
}

// Show Warning Alert
function showWarningAlert(title, message, duration = 6000) {
    const alertHtml = `
        <div class="alert-item bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-yellow-800">${title}</h3>
                    <p class="text-sm text-yellow-700 mt-1">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="close-alert text-yellow-400 hover:text-yellow-600 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    showAlert(alertHtml, duration);
}

// Show Info Alert
function showInfoAlert(title, message, duration = 5000) {
    const alertHtml = `
        <div class="alert-item bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-600 text-xl"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-blue-800">${title}</h3>
                    <p class="text-sm text-blue-700 mt-1">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="close-alert text-blue-400 hover:text-blue-600 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    showAlert(alertHtml, duration);
}

// Show Confirmation Dialog
function showConfirmDialog(title, message, onConfirm, onCancel = null) {
    const dialogHtml = `
        <div id="confirm-dialog" class="fixed inset-0 z-50 flex items-center justify-center p-4">
            <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
            <div class="relative bg-white rounded-xl shadow-2xl max-w-md w-full mx-auto transform transition-all">
                <div class="p-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4 flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">${title}</h3>
                            <p class="text-sm text-gray-600">${message}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-6 py-4 rounded-b-xl flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0">
                    <button id="confirm-no" type="button" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                        Cancel
                    </button>
                    <button id="confirm-yes" type="button" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                        <i class="fas fa-trash mr-2"></i>
                        Yes, Delete
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHtml);

    const dialog = document.getElementById('confirm-dialog');
    const yesBtn = document.getElementById('confirm-yes');
    const noBtn = document.getElementById('confirm-no');

    yesBtn.onclick = function() {
        dialog.remove();
        if (onConfirm) onConfirm();
    };

    noBtn.onclick = function() {
        dialog.remove();
        if (onCancel) onCancel();
    };

    // Close on backdrop click
    dialog.onclick = function(e) {
        if (e.target === dialog) {
            dialog.remove();
            if (onCancel) onCancel();
        }
    };
}

// Core alert display function
function showAlert(alertHtml, duration) {
    const container = document.getElementById('alert-container');
    container.insertAdjacentHTML('beforeend', alertHtml);

    const alertElement = container.lastElementChild;

    // Animate in
    setTimeout(() => {
        alertElement.classList.remove('translate-x-full', 'opacity-0');
        alertElement.classList.add('translate-x-0', 'opacity-100');
    }, 100);

    // Add close button functionality
    const closeBtn = alertElement.querySelector('.close-alert');
    closeBtn.onclick = function() {
        removeAlert(alertElement);
    };

    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeAlert(alertElement);
        }, duration);
    }
}

// Remove alert with animation
function removeAlert(alertElement) {
    alertElement.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
        if (alertElement.parentNode) {
            alertElement.remove();
        }
    }, 300);
}

// Legacy notification function for backward compatibility
function showNotification(message, type = 'info', duration = 3000) {
    switch (type) {
        case 'success':
            showSuccessAlert('Success', message, duration);
            break;
        case 'error':
            showErrorAlert('Error', message, duration);
            break;
        case 'warning':
            showWarningAlert('Warning', message, duration);
            break;
        default:
            showInfoAlert('Info', message, duration);
    }
}

// Loading state utility
function setLoadingState(element, loading = true) {
    if (loading) {
        element.disabled = true;
        element.dataset.originalText = element.innerHTML;
        element.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
    } else {
        element.disabled = false;
        element.innerHTML = element.dataset.originalText || element.innerHTML;
    }
}

// Format time utility
function formatTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);
    
    if (diffInSeconds < 60) {
        return `${diffInSeconds} seconds ago`;
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    }
}

// Format bytes utility
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// API utility functions
const API = {
    // Base API configuration
    baseURL: '/api', // Adjust based on your GenieACS API endpoint
    
    // Generic API call function
    async call(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`API Error: ${response.status} ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            showNotification(`API Error: ${error.message}`, 'error');
            throw error;
        }
    },
    
    // Device related API calls
    devices: {
        getAll: () => API.call('/devices'),
        getById: (id) => API.call(`/devices/${id}`),
        refresh: (id) => API.call(`/devices/${id}/refresh`, { method: 'POST' }),
        reboot: (id) => API.call(`/devices/${id}/reboot`, { method: 'POST' }),
        delete: (id) => API.call(`/devices/${id}`, { method: 'DELETE' })
    },
    
    // Stats API calls
    stats: {
        getDashboard: () => API.call('/stats/dashboard'),
        getDevices: () => API.call('/stats/devices')
    }
};

// Initialize application
function initializeApp() {
    console.log('Initializing GenieACS Panel...');

    // Initialize alert system
    initializeAlertSystem();

    // Set up event listeners
    setupEventListeners();

    // Initialize tooltips if needed
    initializeTooltips();

    console.log('GenieACS Panel initialized successfully');
}

function setupEventListeners() {
    // Close sidebar when clicking overlay
    const overlay = document.getElementById('mobile-overlay');
    if (overlay) {
        overlay.addEventListener('click', closeSidebar);
    }
    
    // Close sidebar on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            closeSidebar();
        }
    });
    
    // Handle search input
    const searchInput = document.querySelector('input[placeholder="Search devices..."]');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value;
            if (query.length > 2) {
                console.log('Searching for:', query);
                // Implement search functionality
            }
        });
    }

    // Mobile optimizations
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');

        // Prevent zoom on input focus for iOS
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], textarea, select');
        inputs.forEach(input => {
            if (!input.style.fontSize) {
                input.style.fontSize = '16px'; // Prevent zoom on iOS
            }
        });
    }

    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            if (window.innerWidth >= 1024) {
                closeSidebar();
            }
        }, 100);
    });
}

function initializeTooltips() {
    // Add tooltip functionality if needed
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const text = event.target.dataset.tooltip;
    // Implement tooltip display
}

function hideTooltip(event) {
    // Implement tooltip hide
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeApp);

// Global error handler
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);
    showNotification('An unexpected error occurred', 'error');
});

// Export utilities for use in other scripts
window.GenieACS = {
    toggleSidebar,
    closeSidebar,
    toggleSwitch,
    showNotification,
    showSuccessAlert,
    showErrorAlert,
    showWarningAlert,
    showInfoAlert,
    showConfirmDialog,
    setLoadingState,
    formatTimeAgo,
    formatBytes,
    API
};
