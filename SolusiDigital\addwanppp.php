<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Catch any fatal errors and return JSON
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && $error['type'] === E_ERROR) {
        error_log("SAFE PPP: Fatal error occurred: " . $error['message']);
        if (!headers_sent()) {
            http_response_code(500);
            echo json_encode([
                'error' => 'Internal server error',
                'debug' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line']
            ]);
        }
    }
});

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Debug: Log raw input
$rawInput = file_get_contents('php://input');
error_log("SAFE PPP: Raw input received: " . $rawInput);

$input = json_decode($rawInput, true);
error_log("SAFE PPP: Decoded input: " . json_encode($input));

$deviceId = $input['deviceId'] ?? null;
$username = $input['username'] ?? '';
$password = $input['password'] ?? '';
$vlan = $input['vlan'] ?? null;
$lanBinding = $input['lanBinding'] ?? null;

error_log("SAFE PPP: Extracted values - deviceId: '$deviceId', username: '$username', password: '" . (empty($password) ? 'EMPTY' : 'PROVIDED') . "', vlan: '$vlan', lanBinding: " . json_encode($lanBinding));

if (!$deviceId) {
    error_log("SAFE PPP: ERROR - Device ID is missing or empty");
    error_log("SAFE PPP: Raw input was: " . $rawInput);
    error_log("SAFE PPP: Decoded input was: " . json_encode($input));
    http_response_code(400);
    echo json_encode([
        'error' => 'Device ID is required',
        'debug' => [
            'raw_input' => $rawInput,
            'decoded_input' => $input,
            'device_id' => $deviceId
        ]
    ]);
    exit;
}

if (empty($username)) {
    http_response_code(400);
    echo json_encode(['error' => 'Username is required']);
    exit;
}

if (empty($password)) {
    http_response_code(400);
    echo json_encode(['error' => 'Password is required']);
    exit;
}

// Validate VLAN if provided (0 is valid for untagged)
if ($vlan !== null && (!is_numeric($vlan) || $vlan < 0 || $vlan > 4094)) {
    http_response_code(400);
    echo json_encode(['error' => 'VLAN ID must be between 0 and 4094 (0 = untagged)']);
    exit;
}

error_log("SAFE PPP: Starting for device: $deviceId");

try {
    // Step 1: Get parsed device data using getdetaildevice.php (more accurate)
    $deviceDetail = getDeviceDetailData($deviceId);
    if (!$deviceDetail) {
        error_log("SAFE PPP: CRITICAL - getDeviceDetailData returned null for device: $deviceId");

        // Return detailed error for debugging
        http_response_code(500);
        echo json_encode([
            'error' => 'Failed to get device detail data',
            'debug' => [
                'device_id' => $deviceId,
                'step' => 'getDeviceDetailData',
                'message' => 'getDeviceDetailData() returned null - check PHP error log for details'
            ]
        ]);
        exit;
    }

    // DEBUG: Log the actual structure received
    error_log("SAFE PPP: Device detail structure keys: " . json_encode(array_keys($deviceDetail)));
    if (isset($deviceDetail['wanInfo'])) {
        error_log("SAFE PPP: wanInfo keys: " . json_encode(array_keys($deviceDetail['wanInfo'])));
        if (isset($deviceDetail['wanInfo']['wanConnections'])) {
            error_log("SAFE PPP: wanConnections keys: " . json_encode(array_keys($deviceDetail['wanInfo']['wanConnections'])));
            error_log("SAFE PPP: wanConnections structure: " . json_encode($deviceDetail['wanInfo']['wanConnections']));
        } else {
            error_log("SAFE PPP: wanConnections NOT FOUND in wanInfo");
        }
    } else {
        error_log("SAFE PPP: wanInfo NOT FOUND in deviceDetail");
    }

    // Step 2: Find SAFE path using parsed WAN connections data
    $safePath = findSafePPPPathFromDetail($deviceDetail);
    if (!$safePath) {
        // Get connection counts for error response
        $wanIPConnections = $deviceDetail['wanInfo']['wanConnections']['wanIPConnections'] ?? [];
        $wanPPPConnections = $deviceDetail['wanInfo']['wanConnections']['wanPPPConnections'] ?? [];

        http_response_code(400);
        echo json_encode([
            'error' => 'No safe PPP slots available',
            'reason' => 'All possible PPP connection slots are occupied',
            'existing_ip_connections' => count($wanIPConnections),
            'existing_ppp_connections' => count($wanPPPConnections),
            'total_connections' => count($wanIPConnections) + count($wanPPPConnections)
        ]);
        exit;
    }

    error_log("SAFE PPP: Using safe path: {$safePath['objectName']}");

    // Step 3: DISABLED - Execute AddObject (for testing path detection)
    error_log("SAFE PPP: [TESTING MODE] Would execute AddObject for: {$safePath['objectName']}");
    error_log("SAFE PPP: [TESTING MODE] Expected path would be: {$safePath['expectedPath']}");

    // Get connection data for testing response - try multiple possible structures
    $wanIPConnections = [];
    $wanPPPConnections = [];

    // Try different possible structures
    if (isset($deviceDetail['wanInfo']['wanConnections']['wanIPConnections'])) {
        $wanIPConnections = $deviceDetail['wanInfo']['wanConnections']['wanIPConnections'];
    } elseif (isset($deviceDetail['wanConnections']['wanIPConnections'])) {
        $wanIPConnections = $deviceDetail['wanConnections']['wanIPConnections'];
    } elseif (isset($deviceDetail['wanIPConnections'])) {
        $wanIPConnections = $deviceDetail['wanIPConnections'];
    }

    if (isset($deviceDetail['wanInfo']['wanConnections']['wanPPPConnections'])) {
        $wanPPPConnections = $deviceDetail['wanInfo']['wanConnections']['wanPPPConnections'];
    } elseif (isset($deviceDetail['wanConnections']['wanPPPConnections'])) {
        $wanPPPConnections = $deviceDetail['wanConnections']['wanPPPConnections'];
    } elseif (isset($deviceDetail['wanPPPConnections'])) {
        $wanPPPConnections = $deviceDetail['wanPPPConnections'];
    }

    $allConnections = array_merge($wanIPConnections, $wanPPPConnections);

    error_log("SAFE PPP: Final counts - IP: " . count($wanIPConnections) . ", PPP: " . count($wanPPPConnections) . ", Total: " . count($allConnections));

    // TESTING COMPLETED - Now enable AddObject functionality
    error_log("SAFE PPP: Testing completed successfully. Proceeding with AddObject...");
    error_log("SAFE PPP: Selected safe path: " . $safePath['expectedPath']);

    // Step 3: Create the WAN PPP Connection object
    $addObjectResult = addWANPPPObject($deviceId, $safePath['objectName']);
    if (!$addObjectResult) {
        http_response_code(500);
        echo json_encode([
            'error' => 'Failed to create WAN PPP Connection object',
            'safe_path' => $safePath['objectName'],
            'expected_path' => $safePath['expectedPath']
        ]);
        exit;
    }

    error_log("SAFE PPP: AddObject successful, proceeding to set parameters...");

    // Step 4: Set the parameters using batch for better performance
    $parameterResults = [];
    $batchParams = [];

    // Prepare Username
    if (!empty($username)) {
        $usernamePath = $safePath['expectedPath'] . '.Username';
        $batchParams[] = [$usernamePath, $username, 'xsd:string'];
        error_log("SAFE PPP: Added username to batch: $usernamePath");
    }

    // Prepare Password
    if (!empty($password)) {
        $passwordPath = $safePath['expectedPath'] . '.Password';
        $batchParams[] = [$passwordPath, $password, 'xsd:string'];
        error_log("SAFE PPP: Added password to batch: $passwordPath");
    }

    // Prepare VLAN if provided
    if ($vlan !== null) {
        // Determine VLAN path based on device type
        $vlanPath = determineVLANPath($deviceDetail, $safePath['expectedPath']);
        if ($vlanPath) {
            $batchParams[] = [$vlanPath, $vlan, 'xsd:unsignedInt'];
            error_log("SAFE PPP: Added VLAN to batch: $vlanPath = $vlan");
        } else {
            error_log("SAFE PPP: Could not determine VLAN path for device");
            $parameterResults['vlan'] = false;
        }
    }

    // Prepare NATEnabled (mandatory)
    $natEnabledPath = $safePath['expectedPath'] . '.NATEnabled';
    $batchParams[] = [$natEnabledPath, '1', 'xsd:boolean'];
    error_log("SAFE PPP: Added NATEnabled to batch: $natEnabledPath");

    // Initialize batch execution flag
    $mainBatchExecuted = false;

    // Set LAN binding if provided (can be done in parallel with main parameters)
    if ($lanBinding && is_array($lanBinding)) {
        error_log("SAFE PPP: Setting LAN binding parameters");
        error_log("SAFE PPP: LAN binding data: " . json_encode($lanBinding));

        // Try to combine LAN binding with main batch if possible
        $lanBindingBatch = getLANBindingBatchParams($deviceId, $safePath['expectedPath'], $lanBinding, $deviceDetail);
        if ($lanBindingBatch && !empty($lanBindingBatch) && !$mainBatchExecuted) {
            // Combine with main batch for maximum efficiency
            $combinedBatch = array_merge($batchParams, $lanBindingBatch);
            error_log("SAFE PPP: Combining LAN binding with main parameters (" . count($combinedBatch) . " total parameters)");
            $combinedResult = setBatchParameters($deviceId, $combinedBatch);
            $parameterResults['combined'] = $combinedResult;
            $parameterResults['lanBinding'] = $combinedResult;
            $mainBatchExecuted = true; // Mark as executed
            error_log("SAFE PPP: Combined batch result: " . ($combinedResult ? 'success' : 'failed'));
        } else {
            // Fallback to separate LAN binding call
            $lanBindingResult = setLANBindingParameters($deviceId, $safePath['expectedPath'], $lanBinding, $deviceDetail);
            $parameterResults['lanBinding'] = $lanBindingResult;
            error_log("SAFE PPP: LAN binding result: " . ($lanBindingResult ? 'success' : 'failed'));
        }
    } else {
        error_log("SAFE PPP: No LAN binding data provided or invalid format");
    }

    // Execute main batch if not yet executed (when no LAN binding or LAN binding was done separately)
    if (!$mainBatchExecuted && !empty($batchParams)) {
        error_log("SAFE PPP: Setting " . count($batchParams) . " main parameters in batch (deferred execution)");
        $batchResult = setBatchParameters($deviceId, $batchParams);
        $parameterResults['batch'] = $batchResult;
        error_log("SAFE PPP: Main batch parameter result: " . ($batchResult ? 'success' : 'failed'));
    }

    // Step 5: Refresh device to ensure changes are applied
    refreshDevice($deviceId);

    // Return success response
    $response = [
        'success' => true,
        'message' => 'WAN PPP Connection created successfully',
        'connection_path' => $safePath['expectedPath'],
        'slot_info' => $safePath['slot'],
        'parameter_results' => $parameterResults,
        'existing_connections_count' => count($allConnections)
    ];

    // Add VLAN info if it was set
    if ($vlan !== null) {
        $response['vlan_set'] = $vlan;
        $response['vlan_path'] = determineVLANPath($deviceDetail, $safePath['expectedPath']);
    }

    echo json_encode($response);

    // COMMENTED OUT FOR TESTING - Original AddObject code:
    /*
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000&connection_request";

    $task = [
        'name' => 'addObject',
        'objectName' => $safePath['objectName']
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $curlResult = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($curlError) {
        error_log("SAFE PPP: cURL error: $curlError");
        $result = false;
    } elseif ($httpCode === 200 || $httpCode === 202) {
        error_log("SAFE PPP: AddObject successful - HTTP $httpCode");
        $result = true;
    } else {
        error_log("SAFE PPP: AddObject failed - HTTP $httpCode, Response: $curlResult");
        $result = false;
    }

    if ($result) {
        // Step 4: Refresh device to ensure new object is available
        error_log("SAFE PPP: Refreshing device to ensure new object is available...");
        refreshDevice($deviceId);

        // Step 5: Set PPP parameters after AddObject success
        error_log("SAFE PPP: Setting PPP parameters for: {$safePath['expectedPath']}");

        $parameterResults = setPPPParameters($deviceId, $safePath['expectedPath'], $username, $password);

        if ($parameterResults['success']) {
            echo json_encode([
                'success' => true,
                'message' => 'PPP Connection created and configured successfully',
                'safe_path' => $safePath['objectName'],
                'expected_full_path' => $safePath['expectedPath'],
                'slot_info' => $safePath['slot'],
                'parameters_set' => $parameterResults['parameters']
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => 'AddObject successful but parameter setting had issues',
                'safe_path' => $safePath['objectName'],
                'expected_full_path' => $safePath['expectedPath'],
                'slot_info' => $safePath['slot'],
                'parameter_errors' => $parameterResults['errors']
            ]);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'AddObject failed']);
    }
    */

} catch (Exception $e) {
    error_log("SAFE PPP Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

// Get device data from GenieACS
function getDeviceData($deviceId) {
    $query = json_encode(['_id' => $deviceId]);
    $apiUrl = GENIEACS_URL . '?query=' . urlencode($query);

    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Reduced timeout for faster response

    $deviceData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200 || !$deviceData) {
        return null;
    }

    $data = json_decode($deviceData, true);
    return !empty($data) ? $data[0] : null;
}

// Find SAFE PPP path that won't overwrite existing connections
function findSafePPPPath($device) {
    error_log("SAFE PPP: Scanning for existing PPP connections...");

    // Scan all possible PPP connection slots
    $occupiedSlots = [];

    for ($wan = 1; $wan <= 4; $wan++) {
        for ($conn = 1; $conn <= 8; $conn++) {
            // Check if any PPP connections exist in this WAN/Connection area
            $basePattern = "InternetGatewayDevice.WANDevice.{$wan}.WANConnectionDevice.{$conn}.WANPPPConnection.";

            foreach ($device as $key => $value) {
                if (strpos($key, $basePattern) === 0 && strpos($key, '.Enable') !== false) {
                    // Extract PPP index from key like "...WANPPPConnection.2.Enable"
                    $parts = explode('.', $key);
                    $pppIndex = null;

                    for ($i = 0; $i < count($parts); $i++) {
                        if ($parts[$i] === 'WANPPPConnection' && isset($parts[$i + 1])) {
                            $pppIndex = $parts[$i + 1];
                            break;
                        }
                    }

                    if ($pppIndex && is_numeric($pppIndex)) {
                        $slotKey = "{$wan}-{$conn}-{$pppIndex}";
                        $occupiedSlots[$slotKey] = true;
                        error_log("SAFE PPP: Found occupied slot: WAN{$wan}.Conn{$conn}.PPP{$pppIndex}");
                    }
                }
            }
        }
    }

    error_log("SAFE PPP: Found " . count($occupiedSlots) . " occupied PPP slots");

    // Find first available slot
    for ($wan = 1; $wan <= 4; $wan++) {
        for ($conn = 1; $conn <= 8; $conn++) {
            for ($ppp = 1; $ppp <= 8; $ppp++) {
                $slotKey = "{$wan}-{$conn}-{$ppp}";

                if (!isset($occupiedSlots[$slotKey])) {
                    // Found safe slot!
                    $safePath = [
                        'objectName' => "InternetGatewayDevice.WANDevice.{$wan}.WANConnectionDevice.{$conn}.WANPPPConnection",
                        'expectedPath' => "InternetGatewayDevice.WANDevice.{$wan}.WANConnectionDevice.{$conn}.WANPPPConnection.{$ppp}",
                        'slot' => [
                            'wan' => $wan,
                            'connection' => $conn,
                            'ppp' => $ppp
                        ]
                    ];

                    error_log("SAFE PPP: Found SAFE slot: WAN{$wan}.Conn{$conn}.PPP{$ppp}");
                    return $safePath;
                }
            }
        }
    }

    error_log("SAFE PPP: No safe slots available");
    return null;
}

// Add WAN PPP Object wrapper function
function addWANPPPObject($deviceId, $objectName) {
    error_log("SAFE PPP: addWANPPPObject called with deviceId: $deviceId, objectName: $objectName");
    return executeAddObject($deviceId, $objectName);
}

// Execute AddObject with safe path
function executeAddObject($deviceId, $objectName) {
    error_log("SAFE PPP: executeAddObject function called with deviceId: $deviceId, objectName: $objectName");

    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000";

    error_log("SAFE PPP: GenieACS URL: $url");

    $task = [
        'name' => 'addObject',
        'objectName' => $objectName
    ];

    error_log("SAFE PPP: Executing AddObject: $objectName");

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("SAFE PPP: cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    if ($httpCode === 200 || $httpCode === 202) {
        error_log("SAFE PPP: AddObject successful - HTTP $httpCode");
        return true;
    }

    error_log("SAFE PPP: AddObject failed - HTTP $httpCode, Response: $result");
    return false;
}

// Set PPP parameters after AddObject
function setPPPParameters($deviceId, $pppPath, $username, $password) {
    error_log("SAFE PPP: Setting parameters for path: $pppPath");
    error_log("SAFE PPP: Using username: $username");

    // Remove Name parameter since it causes "Invalid parameter path" error
    // Name is usually auto-generated by the router
    $parameters = [
        'ConnectionType' => 'PPP_Routed',
        'NATEnabled' => true,
        'Enable' => true,
        'Username' => $username,
        'Password' => $password
    ];

    $results = [
        'success' => true,
        'parameters' => [],
        'errors' => []
    ];

    foreach ($parameters as $param => $value) {
        $fullPath = $pppPath . '.' . $param;
        $success = setParameter($deviceId, $fullPath, $value);

        if ($success) {
            $results['parameters'][] = "$param = $value";
            error_log("SAFE PPP: Successfully set $fullPath = $value");
        } else {
            $results['success'] = false;
            $results['errors'][] = "Failed to set $param";
            error_log("SAFE PPP: Failed to set $fullPath = $value");
        }

        // No delay needed - GenieACS can handle rapid parameter sets
    }

    return $results;
}

// Set WAN PPP Parameter wrapper function
function setWANPPPParameter($deviceId, $parameterPath, $value) {
    error_log("SAFE PPP: setWANPPPParameter called with path: $parameterPath, value: $value");
    return setParameter($deviceId, $parameterPath, $value);
}

// Function to determine VLAN path based on device type
function determineVLANPath($deviceDetail, $connectionPath) {
    $deviceInfo = $deviceDetail['deviceInfo'] ?? [];
    $manufacturer = strtolower($deviceInfo['manufacturer'] ?? '');

    if (strpos($manufacturer, 'huawei') !== false) {
        // Huawei: VLAN is at connection level
        // InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.1.X_HW_VLAN
        return $connectionPath . '.X_HW_VLAN';
    } elseif (strpos($manufacturer, 'zte') !== false) {
        // ZTE: VLAN is at WANConnectionDevice level
        // Extract WANConnectionDevice path from connection path
        // From: InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.1
        // To: InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.X_CT-COM_WANEponLinkConfig.VLANIDMark
        if (preg_match('/(InternetGatewayDevice\.WANDevice\.\d+\.WANConnectionDevice\.\d+)\.WANPPPConnection\.\d+/', $connectionPath, $matches)) {
            $wanConnDevicePath = $matches[1];
            return $wanConnDevicePath . '.X_CT-COM_WANEponLinkConfig.VLANIDMark';
        }
    }

    // Default fallback to Huawei format
    return $connectionPath . '.X_HW_VLAN';
}

// Set individual parameter
function setParameter($deviceId, $parameterPath, $value) {
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000&connection_request";

    $task = [
        'name' => 'setParameterValues',
        'parameterValues' => [
            [$parameterPath, $value]
        ]
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("SAFE PPP: Parameter set cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    return ($httpCode === 200 || $httpCode === 202);
}

// Set individual parameter with data type
function setParameterWithType($deviceId, $parameterPath, $value, $dataType = 'xsd:string') {
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000&connection_request";

    $task = [
        'name' => 'setParameterValues',
        'parameterValues' => [
            [$parameterPath, $value, $dataType]
        ]
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("SAFE PPP: Parameter set with type cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    return ($httpCode === 200 || $httpCode === 202);
}

// Set multiple parameters in one batch (faster)
function setBatchParameters($deviceId, $parameterArray) {
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000&connection_request";

    $task = [
        'name' => 'setParameterValues',
        'parameterValues' => $parameterArray
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("SAFE PPP: Batch parameter set cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    return ($httpCode === 200 || $httpCode === 202);
}

// Refresh device to ensure new objects are available
function refreshDevice($deviceId) {
    $genieacsBaseUrl = str_replace('/devices', '', GENIEACS_URL);
    $url = "$genieacsBaseUrl/devices/" . urlencode($deviceId) . "/tasks?timeout=30000&connection_request";

    $task = [
        'name' => 'refreshObject',
        'objectName' => 'InternetGatewayDevice.WANDevice.'
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    error_log("SAFE PPP: Device refresh - HTTP $httpCode");
    return ($httpCode === 200 || $httpCode === 202);
}

// Function to get parsed device detail data (using getdetaildevice.php logic)
function getDeviceDetailData($deviceId) {
    error_log("SAFE PPP: Getting device detail data for: $deviceId");

    // Try direct include first (more reliable for internal calls)
    $directResult = getDeviceDetailDataDirect($deviceId);
    if ($directResult !== null) {
        error_log("SAFE PPP: Successfully got data via direct include");
        return $directResult;
    }

    error_log("SAFE PPP: Direct include failed, trying cURL...");

    // Get current protocol and host
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . '://' . $host;

    // Call getdetaildevice.php internally to get parsed data
    // Note: getdetaildevice.php uses 'id' parameter, not 'deviceId'
    $url = $baseUrl . '/SolusiDigital/getdetaildevice.php?id=' . urlencode($deviceId);

    error_log("SAFE PPP: Protocol: $protocol");
    error_log("SAFE PPP: Host: $host");
    error_log("SAFE PPP: Base URL: $baseUrl");
    error_log("SAFE PPP: Full URL: $url");
    error_log("SAFE PPP: Device ID (raw): $deviceId");
    error_log("SAFE PPP: Device ID (encoded): " . urlencode($deviceId));

    // Test if cURL is available
    if (!function_exists('curl_init')) {
        error_log("SAFE PPP: CRITICAL - cURL is not available!");
        return null;
    }

    $ch = curl_init($url);
    if (!$ch) {
        error_log("SAFE PPP: CRITICAL - curl_init failed!");
        return null;
    }

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Reduced timeout for faster response
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For local development
    curl_setopt($ch, CURLOPT_USERAGENT, 'AddWANPPP-Internal-Call/1.0');

    error_log("SAFE PPP: Executing cURL request...");
    $result = curl_exec($ch);
    error_log("SAFE PPP: cURL execution completed");

    if (curl_errno($ch)) {
        $curlError = curl_error($ch);
        error_log("SAFE PPP: cURL error: $curlError");
        curl_close($ch);
        return null;
    }

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    error_log("SAFE PPP: HTTP response code: $httpCode");

    if ($httpCode !== 200) {
        error_log("SAFE PPP: HTTP error getting device detail: $httpCode");
        error_log("SAFE PPP: Response body: " . substr($result, 0, 500));
        return null;
    }

    $data = json_decode($result, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("SAFE PPP: JSON decode error: " . json_last_error_msg());
        error_log("SAFE PPP: Raw response: " . substr($result, 0, 500));
        return null;
    }

    error_log("SAFE PPP: Successfully got device detail data");
    return $data;
}

// Find SAFE PPP path using parsed device detail data
function findSafePPPPathFromDetail($deviceDetail) {
    error_log("SAFE PPP: Scanning existing WAN connections from parsed data...");
    error_log("SAFE PPP: Full deviceDetail structure: " . json_encode($deviceDetail));

    $occupiedSlots = [];
    $occupiedConnDevices = []; // Track which connection devices have what types

    // DEBUG: Check what's actually in the structure
    if (!isset($deviceDetail['wanInfo'])) {
        error_log("SAFE PPP: ERROR - wanInfo not found in deviceDetail");
        return findFirstAvailableSlot(); // Fallback to simple slot finding
    }

    if (!isset($deviceDetail['wanInfo']['wanConnections'])) {
        error_log("SAFE PPP: ERROR - wanConnections not found in wanInfo");
        return findFirstAvailableSlot(); // Fallback to simple slot finding
    }

    // Get both IP and PPP connections from the correct structure
    $wanIPConnections = $deviceDetail['wanInfo']['wanConnections']['wanIPConnections'] ?? [];
    $wanPPPConnections = $deviceDetail['wanInfo']['wanConnections']['wanPPPConnections'] ?? [];

    // Combine all connections
    $allConnections = array_merge($wanIPConnections, $wanPPPConnections);

    error_log("SAFE PPP: Found " . count($wanIPConnections) . " IP connections");
    error_log("SAFE PPP: Found " . count($wanPPPConnections) . " PPP connections");
    error_log("SAFE PPP: Total " . count($allConnections) . " existing WAN connections");

    // Parse existing connections to find occupied WANConnectionDevices
    // Key insight: Each WANConnectionDevice can only have ONE connection (either IP or PPP)
    $occupiedConnectionDevices = [];

    foreach ($allConnections as $conn) {
        $path = $conn['path'] ?? '';
        $connType = $conn['type'] ?? '';

        error_log("SAFE PPP: Checking connection - Path: {$path}, Type: {$connType}");

        // Parse path like "InternetGatewayDevice.WANDevice.1.WANConnectionDevice.2.WANPPPConnection.1"
        if (preg_match('/^InternetGatewayDevice\.WANDevice\.(\d+)\.WANConnectionDevice\.(\d+)\.WAN(PPP|IP)Connection\.(\d+)$/', $path, $matches)) {
            $wan = (int)$matches[1];
            $connDev = (int)$matches[2];
            $pathConnType = $matches[3]; // PPP or IP from path
            $connIndex = (int)$matches[4];

            // Mark this WANConnectionDevice as occupied
            $connDevKey = "{$wan}.{$connDev}";
            $occupiedConnectionDevices[$connDevKey] = [
                'wan' => $wan,
                'connection' => $connDev,
                'type' => $pathConnType,
                'instance' => $connIndex,
                'path' => $path
            ];

            error_log("SAFE PPP: WANConnectionDevice WAN{$wan}.Conn{$connDev} is occupied by {$pathConnType}{$connIndex}");

        } else {
            error_log("SAFE PPP: WARNING - Could not parse path: {$path}");
        }
    }

    error_log("SAFE PPP: Found " . count($occupiedConnectionDevices) . " occupied WANConnectionDevices");
    error_log("SAFE PPP: Occupied WANConnectionDevices: " . json_encode($occupiedConnectionDevices));

    // Find first available WANConnectionDevice
    // Logic: Look for a WANConnectionDevice that is completely free
    for ($wan = 1; $wan <= 4; $wan++) {
        for ($conn = 1; $conn <= 8; $conn++) {
            $connDevKey = "{$wan}.{$conn}";

            // Check if this WANConnectionDevice is free
            if (!isset($occupiedConnectionDevices[$connDevKey])) {
                // This WANConnectionDevice is completely free - use it for PPP
                $safePath = [
                    'objectName' => "InternetGatewayDevice.WANDevice.{$wan}.WANConnectionDevice.{$conn}.WANPPPConnection",
                    'expectedPath' => "InternetGatewayDevice.WANDevice.{$wan}.WANConnectionDevice.{$conn}.WANPPPConnection.1",
                    'slot' => [
                        'wan' => $wan,
                        'connection' => $conn,
                        'ppp' => 1
                    ]
                ];

                error_log("SAFE PPP: Found FREE WANConnectionDevice: WAN{$wan}.Conn{$conn} - will create PPPConnection.1");
                return $safePath;
            } else {
                $existing = $occupiedConnectionDevices[$connDevKey];
                error_log("SAFE PPP: WANConnectionDevice WAN{$wan}.Conn{$conn} is occupied by {$existing['type']}{$existing['instance']}");
            }
        }
    }

    error_log("SAFE PPP: No safe slots available");
    return null;
}

// Fallback function to find first available WANConnectionDevice (simple approach)
function findFirstAvailableSlot() {
    error_log("SAFE PPP: Using fallback - finding first available WANConnectionDevice");

    // Start from WAN1.Conn2 to avoid common conflicts (Conn1 often used)
    for ($wan = 1; $wan <= 4; $wan++) {
        for ($conn = 2; $conn <= 8; $conn++) { // Start from Conn2, not Conn1
            $safePath = [
                'objectName' => "InternetGatewayDevice.WANDevice.{$wan}.WANConnectionDevice.{$conn}.WANPPPConnection",
                'expectedPath' => "InternetGatewayDevice.WANDevice.{$wan}.WANConnectionDevice.{$conn}.WANPPPConnection.1",
                'slot' => [
                    'wan' => $wan,
                    'connection' => $conn,
                    'ppp' => 1
                ]
            ];

            error_log("SAFE PPP: Fallback selected WANConnectionDevice: WAN{$wan}.Conn{$conn}");
            return $safePath;
        }
    }

    return null;
}

// Function to get device detail data via direct include (more reliable)
function getDeviceDetailDataDirect($deviceId) {
    error_log("SAFE PPP: Trying direct include method for device: $deviceId");

    try {
        // Capture output buffer
        ob_start();

        // Set GET parameter for getdetaildevice.php
        $_GET['id'] = $deviceId;

        // Include the file directly
        include 'getdetaildevice.php';

        // Get the output
        $output = ob_get_clean();

        error_log("SAFE PPP: Direct include raw output (first 1000 chars): " . substr($output, 0, 1000));

        // Decode JSON
        $data = json_decode($output, true);

        if (json_last_error() === JSON_ERROR_NONE && $data !== null) {
            error_log("SAFE PPP: Direct include successful");
            return $data;
        } else {
            error_log("SAFE PPP: Direct include - JSON decode failed: " . json_last_error_msg());
            error_log("SAFE PPP: Direct include - Raw output: " . substr($output, 0, 500));
            return null;
        }

    } catch (Exception $e) {
        error_log("SAFE PPP: Direct include failed: " . $e->getMessage());
        return null;
    } catch (Error $e) {
        error_log("SAFE PPP: Direct include error: " . $e->getMessage());
        return null;
    }
}

// Function to get LAN binding parameters as batch array (for combining with main parameters)
function getLANBindingBatchParams($deviceId, $connectionPath, $lanBinding, $deviceDetail) {
    try {
        error_log("SAFE PPP: Getting LAN binding batch params for connection: $connectionPath");

        // Determine device type from device detail
        $deviceInfo = $deviceDetail['deviceInfo'] ?? [];
        $manufacturer = strtolower($deviceInfo['manufacturer'] ?? '');

        error_log("SAFE PPP: Device info: " . json_encode($deviceInfo));
        error_log("SAFE PPP: Manufacturer: $manufacturer");

        if (strpos($manufacturer, 'huawei') !== false) {
            // For Huawei, return individual enable flags as batch
            error_log("SAFE PPP: Preparing Huawei LAN binding batch params");

            $batchParams = [
                [$connectionPath . '.X_HW_LANBIND.Lan1Enable', ($lanBinding['lan1'] === true || $lanBinding['lan1'] === 'true' || $lanBinding['lan1'] === 1) ? '1' : '0', 'xsd:unsignedInt'],
                [$connectionPath . '.X_HW_LANBIND.Lan2Enable', ($lanBinding['lan2'] === true || $lanBinding['lan2'] === 'true' || $lanBinding['lan2'] === 1) ? '1' : '0', 'xsd:unsignedInt'],
                [$connectionPath . '.X_HW_LANBIND.Lan3Enable', ($lanBinding['lan3'] === true || $lanBinding['lan3'] === 'true' || $lanBinding['lan3'] === 1) ? '1' : '0', 'xsd:unsignedInt'],
                [$connectionPath . '.X_HW_LANBIND.Lan4Enable', ($lanBinding['lan4'] === true || $lanBinding['lan4'] === 'true' || $lanBinding['lan4'] === 1) ? '1' : '0', 'xsd:unsignedInt'],
                [$connectionPath . '.X_HW_LANBIND.SSID1Enable', ($lanBinding['ssid1'] === true || $lanBinding['ssid1'] === 'true' || $lanBinding['ssid1'] === 1) ? '1' : '0', 'xsd:unsignedInt'],
                [$connectionPath . '.X_HW_LANBIND.SSID2Enable', ($lanBinding['ssid2'] === true || $lanBinding['ssid2'] === 'true' || $lanBinding['ssid2'] === 1) ? '1' : '0', 'xsd:unsignedInt'],
                [$connectionPath . '.X_HW_LANBIND.SSID3Enable', ($lanBinding['ssid3'] === true || $lanBinding['ssid3'] === 'true' || $lanBinding['ssid3'] === 1) ? '1' : '0', 'xsd:unsignedInt'],
                [$connectionPath . '.X_HW_LANBIND.SSID4Enable', ($lanBinding['ssid4'] === true || $lanBinding['ssid4'] === 'true' || $lanBinding['ssid4'] === 1) ? '1' : '0', 'xsd:unsignedInt']
            ];

            return $batchParams;

        } elseif (strpos($manufacturer, 'zte') !== false) {
            // For ZTE, build interface string and return as single batch param
            error_log("SAFE PPP: Preparing ZTE LAN binding batch params");

            $interfaces = [];

            // Add LAN interfaces
            if ($lanBinding['lan1'] === true || $lanBinding['lan1'] === 'true' || $lanBinding['lan1'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1';
            if ($lanBinding['lan2'] === true || $lanBinding['lan2'] === 'true' || $lanBinding['lan2'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.2';
            if ($lanBinding['lan3'] === true || $lanBinding['lan3'] === 'true' || $lanBinding['lan3'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.3';
            if ($lanBinding['lan4'] === true || $lanBinding['lan4'] === 'true' || $lanBinding['lan4'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.4';

            // Add WLAN interfaces
            if ($lanBinding['ssid1'] === true || $lanBinding['ssid1'] === 'true' || $lanBinding['ssid1'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1';
            if ($lanBinding['ssid2'] === true || $lanBinding['ssid2'] === 'true' || $lanBinding['ssid2'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2';
            if ($lanBinding['ssid3'] === true || $lanBinding['ssid3'] === 'true' || $lanBinding['ssid3'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3';
            if ($lanBinding['ssid4'] === true || $lanBinding['ssid4'] === 'true' || $lanBinding['ssid4'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4';

            $interfaceString = implode(',', $interfaces);

            // Use X_CT-COM_LanInterface as default for ZTE
            $interfacePath = $connectionPath . '.X_CT-COM_LanInterface';

            return [[$interfacePath, $interfaceString, 'xsd:string']];

        } else {
            error_log("SAFE PPP: Unsupported manufacturer for LAN binding batch: $manufacturer");
            return [];
        }

    } catch (Exception $e) {
        error_log("SAFE PPP: Error getting LAN binding batch params: " . $e->getMessage());
        return [];
    }
}

// Function to set LAN binding parameters
function setLANBindingParameters($deviceId, $connectionPath, $lanBinding, $deviceDetail) {
    try {
        error_log("SAFE PPP: Setting LAN binding for connection: $connectionPath");

        // Determine device type from device detail
        $deviceInfo = $deviceDetail['deviceInfo'] ?? [];
        $manufacturer = strtolower($deviceInfo['manufacturer'] ?? '');

        error_log("SAFE PPP: Device info: " . json_encode($deviceInfo));
        error_log("SAFE PPP: Manufacturer: $manufacturer");

        if (strpos($manufacturer, 'huawei') !== false) {
            // For Huawei, set individual enable flags
            error_log("SAFE PPP: Setting Huawei LAN binding");

            $updates = [
                ['path' => $connectionPath . '.X_HW_LANBIND.Lan1Enable', 'value' => ($lanBinding['lan1'] === true || $lanBinding['lan1'] === 'true' || $lanBinding['lan1'] === 1) ? '1' : '0'],
                ['path' => $connectionPath . '.X_HW_LANBIND.Lan2Enable', 'value' => ($lanBinding['lan2'] === true || $lanBinding['lan2'] === 'true' || $lanBinding['lan2'] === 1) ? '1' : '0'],
                ['path' => $connectionPath . '.X_HW_LANBIND.Lan3Enable', 'value' => ($lanBinding['lan3'] === true || $lanBinding['lan3'] === 'true' || $lanBinding['lan3'] === 1) ? '1' : '0'],
                ['path' => $connectionPath . '.X_HW_LANBIND.Lan4Enable', 'value' => ($lanBinding['lan4'] === true || $lanBinding['lan4'] === 'true' || $lanBinding['lan4'] === 1) ? '1' : '0'],
                ['path' => $connectionPath . '.X_HW_LANBIND.SSID1Enable', 'value' => ($lanBinding['ssid1'] === true || $lanBinding['ssid1'] === 'true' || $lanBinding['ssid1'] === 1) ? '1' : '0'],
                ['path' => $connectionPath . '.X_HW_LANBIND.SSID2Enable', 'value' => ($lanBinding['ssid2'] === true || $lanBinding['ssid2'] === 'true' || $lanBinding['ssid2'] === 1) ? '1' : '0'],
                ['path' => $connectionPath . '.X_HW_LANBIND.SSID3Enable', 'value' => ($lanBinding['ssid3'] === true || $lanBinding['ssid3'] === 'true' || $lanBinding['ssid3'] === 1) ? '1' : '0'],
                ['path' => $connectionPath . '.X_HW_LANBIND.SSID4Enable', 'value' => ($lanBinding['ssid4'] === true || $lanBinding['ssid4'] === 'true' || $lanBinding['ssid4'] === 1) ? '1' : '0']
            ];

            // Use batch parameter setting for faster execution
            $batchParams = [];
            foreach ($updates as $update) {
                $batchParams[] = [$update['path'], $update['value'], 'xsd:unsignedInt'];
            }

            error_log("SAFE PPP: Setting Huawei LAN binding parameters in batch");
            $result = setBatchParameters($deviceId, $batchParams);

            if ($result) {
                error_log("SAFE PPP: Successfully set all Huawei LAN binding parameters in batch");
            } else {
                error_log("SAFE PPP: Failed to set Huawei LAN binding parameters in batch");
            }

            return $result;

        } elseif (strpos($manufacturer, 'zte') !== false) {
            // For ZTE, build interface string
            error_log("SAFE PPP: Setting ZTE LAN binding");

            $interfaces = [];

            // Add LAN interfaces
            if ($lanBinding['lan1'] === true || $lanBinding['lan1'] === 'true' || $lanBinding['lan1'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1';
            if ($lanBinding['lan2'] === true || $lanBinding['lan2'] === 'true' || $lanBinding['lan2'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.2';
            if ($lanBinding['lan3'] === true || $lanBinding['lan3'] === 'true' || $lanBinding['lan3'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.3';
            if ($lanBinding['lan4'] === true || $lanBinding['lan4'] === 'true' || $lanBinding['lan4'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.4';

            // Add WLAN interfaces
            if ($lanBinding['ssid1'] === true || $lanBinding['ssid1'] === 'true' || $lanBinding['ssid1'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1';
            if ($lanBinding['ssid2'] === true || $lanBinding['ssid2'] === 'true' || $lanBinding['ssid2'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2';
            if ($lanBinding['ssid3'] === true || $lanBinding['ssid3'] === 'true' || $lanBinding['ssid3'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3';
            if ($lanBinding['ssid4'] === true || $lanBinding['ssid4'] === 'true' || $lanBinding['ssid4'] === 1) $interfaces[] = 'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4';

            $interfaceString = implode(',', $interfaces);

            // Use X_CT-COM_LanInterface as default for ZTE
            $interfacePath = $connectionPath . '.X_CT-COM_LanInterface';

            error_log("SAFE PPP: Setting ZTE interface string: $interfaceString");
            // Use batch parameter setting for consistency
            $batchParams = [[$interfacePath, $interfaceString, 'xsd:string']];
            $result = setBatchParameters($deviceId, $batchParams);

            if (!$result) {
                error_log("SAFE PPP: Failed to set ZTE LAN binding parameter: $interfacePath");
            } else {
                error_log("SAFE PPP: Successfully set ZTE LAN binding parameter: $interfacePath = $interfaceString");
            }

            return $result;

        } else {
            error_log("SAFE PPP: Unsupported manufacturer for LAN binding: $manufacturer");
            return false;
        }

    } catch (Exception $e) {
        error_log("SAFE PPP: Error setting LAN binding: " . $e->getMessage());
        return false;
    }
}
?>