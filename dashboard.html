<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GenieACS Panel - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563EB',
                        secondary: '#1D4ED8',
                        accent: '#F59E0B',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans antialiased">
    <div id="app" class="min-h-screen flex">
        <!-- Mobile menu overlay -->
        <div id="mobile-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 hidden lg:hidden"></div>
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 px-4 bg-gradient-to-r from-blue-600 to-blue-800">
                <div class="flex items-center">
                    <i class="fas fa-network-wired text-white text-xl mr-2"></i>
                    <h1 class="text-xl font-bold text-white">GenieACS Panel</h1>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <!-- Dashboard -->
                    <a href="#" onclick="showPage('dashboard')" id="nav-dashboard"
                       class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 bg-blue-600 text-white">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    <!-- Device -->
                    <a href="#" onclick="showPage('device')" id="nav-device"
                       class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-router mr-3"></i>
                        Device
                    </a>

                    <!-- Settings -->
                    <a href="#" onclick="showPage('settings')" id="nav-settings"
                       class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-cog mr-3"></i>
                        Settings
                    </a>
                </div>
            </nav>

            <!-- User Profile -->
            <div class="absolute bottom-0 w-full p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-700">Admin User</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-4 py-4">
                    <div class="flex items-center">
                        <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 mr-4">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 id="page-title" class="text-xl font-semibold text-gray-800">Dashboard</h2>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <button class="relative text-gray-500 hover:text-gray-700 transition-colors">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
                        </button>

                        <!-- Search -->
                        <div class="hidden md:block">
                            <div class="relative">
                                <input type="text" placeholder="Search devices..."
                                       class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64">
                                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Profile dropdown -->
                        <div class="relative">
                            <button class="flex items-center text-gray-500 hover:text-gray-700">
                                <i class="fas fa-user-circle text-2xl"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <div class="container mx-auto px-6 py-8">
                    <!-- //dashboard -->
                    <div id="dashboard-page" class="page-content">
                        <!-- Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="flex items-center">
                                    <div class="p-3 bg-blue-100 rounded-xl">
                                        <i class="fas fa-router text-blue-600 text-2xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Total Devices</p>
                                        <p class="text-3xl font-bold text-gray-900">1,234</p>
                                        <p class="text-xs text-green-600 mt-1">
                                            <i class="fas fa-arrow-up mr-1"></i>+12 this month
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="flex items-center">
                                    <div class="p-3 bg-green-100 rounded-xl">
                                        <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Online</p>
                                        <p class="text-3xl font-bold text-gray-900">1,156</p>
                                        <p class="text-xs text-green-600 mt-1">
                                            <i class="fas fa-arrow-up mr-1"></i>93.7% uptime
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="flex items-center">
                                    <div class="p-3 bg-red-100 rounded-xl">
                                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Offline</p>
                                        <p class="text-3xl font-bold text-gray-900">78</p>
                                        <p class="text-xs text-red-600 mt-1">
                                            <i class="fas fa-arrow-down mr-1"></i>6.3% down
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
                                <div class="flex items-center">
                                    <div class="p-3 bg-yellow-100 rounded-xl">
                                        <i class="fas fa-clock text-yellow-600 text-2xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Pending Tasks</p>
                                        <p class="text-3xl font-bold text-gray-900">23</p>
                                        <p class="text-xs text-yellow-600 mt-1">
                                            <i class="fas fa-clock mr-1"></i>In queue
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity & Quick Actions -->
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Recent Activity -->
                            <div class="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</button>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-4">
                                        <div class="flex items-start p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                            <div class="w-3 h-3 bg-green-500 rounded-full mr-4 mt-1 flex-shrink-0"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900">Device CPE-001234 came online</p>
                                                <p class="text-xs text-gray-500 mt-1">Router reconnected successfully • 2 minutes ago</p>
                                            </div>
                                        </div>
                                        <div class="flex items-start p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-4 mt-1 flex-shrink-0"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900">Firmware update completed</p>
                                                <p class="text-xs text-gray-500 mt-1">15 devices updated to v2.1.3 • 5 minutes ago</p>
                                            </div>
                                        </div>
                                        <div class="flex items-start p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                            <div class="w-3 h-3 bg-red-500 rounded-full mr-4 mt-1 flex-shrink-0"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900">Device CPE-005678 went offline</p>
                                                <p class="text-xs text-gray-500 mt-1">Connection timeout detected • 10 minutes ago</p>
                                            </div>
                                        </div>
                                        <div class="flex items-start p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-4 mt-1 flex-shrink-0"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900">Configuration backup created</p>
                                                <p class="text-xs text-gray-500 mt-1">Auto backup for 234 devices • 15 minutes ago</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                                </div>
                                <div class="p-6 space-y-3">
                                    <button class="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-plus mr-2"></i>
                                        Add Device
                                    </button>
                                    <button class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                                        <i class="fas fa-download mr-2"></i>
                                        Backup Config
                                    </button>
                                    <button class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                                        <i class="fas fa-sync mr-2"></i>
                                        Refresh All
                                    </button>
                                    <button class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                                        <i class="fas fa-chart-bar mr-2"></i>
                                        View Reports
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- //device -->
                    <div id="device-page" class="page-content hidden">
                        <!-- Device Stats -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">Active Devices</p>
                                        <p class="text-2xl font-bold text-green-600">1,156</p>
                                    </div>
                                    <div class="p-3 bg-green-100 rounded-xl">
                                        <i class="fas fa-wifi text-green-600 text-xl"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">Inactive Devices</p>
                                        <p class="text-2xl font-bold text-red-600">78</p>
                                    </div>
                                    <div class="p-3 bg-red-100 rounded-xl">
                                        <i class="fas fa-exclamation-circle text-red-600 text-xl"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">Total Bandwidth</p>
                                        <p class="text-2xl font-bold text-blue-600">2.4 Gbps</p>
                                    </div>
                                    <div class="p-3 bg-blue-100 rounded-xl">
                                        <i class="fas fa-tachometer-alt text-blue-600 text-xl"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Device Management -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                <h3 class="text-lg font-semibold text-gray-900">Device Management</h3>
                                <div class="flex flex-col sm:flex-row gap-2">
                                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                                        <i class="fas fa-filter mr-2"></i>Filter
                                    </button>
                                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-plus mr-2"></i>Add Device
                                    </button>
                                </div>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Seen</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CPE-001234</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TP-Link Archer C7</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.1</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    <i class="fas fa-circle mr-1 text-xs"></i>Online
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 minutes ago</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">
                                                    <i class="fas fa-edit mr-1"></i>Edit
                                                </button>
                                                <button class="text-green-600 hover:text-green-900 mr-3">
                                                    <i class="fas fa-sync mr-1"></i>Refresh
                                                </button>
                                                <button class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CPE-005678</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Huawei HG8245H</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.2</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    <i class="fas fa-circle mr-1 text-xs"></i>Offline
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10 minutes ago</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">
                                                    <i class="fas fa-edit mr-1"></i>Edit
                                                </button>
                                                <button class="text-yellow-600 hover:text-yellow-900 mr-3">
                                                    <i class="fas fa-power-off mr-1"></i>Reboot
                                                </button>
                                                <button class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CPE-009876</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Mikrotik hAP ac²</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.3</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    <i class="fas fa-circle mr-1 text-xs"></i>Online
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 hour ago</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">
                                                    <i class="fas fa-edit mr-1"></i>Edit
                                                </button>
                                                <button class="text-green-600 hover:text-green-900 mr-3">
                                                    <i class="fas fa-sync mr-1"></i>Refresh
                                                </button>
                                                <button class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                <div class="text-sm text-gray-500">
                                    Showing 1 to 3 of 1,234 devices
                                </div>
                                <div class="flex space-x-2">
                                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Previous</button>
                                    <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
                                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Next</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- //settings -->
                    <div id="settings-page" class="page-content hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- General Settings -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <i class="fas fa-cog mr-2 text-blue-600"></i>General Settings
                                    </h3>
                                </div>
                                <div class="p-6 space-y-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Server Name</label>
                                        <input type="text" value="GenieACS Server"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Auto Refresh Interval</label>
                                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option>30 seconds</option>
                                            <option selected>1 minute</option>
                                            <option>5 minutes</option>
                                            <option>10 minutes</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option>UTC</option>
                                            <option selected>Asia/Jakarta</option>
                                            <option>America/New_York</option>
                                            <option>Europe/London</option>
                                        </select>
                                    </div>
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="text-sm font-medium text-gray-700">Enable Notifications</p>
                                            <p class="text-xs text-gray-500">Get notified about device status changes</p>
                                        </div>
                                        <button onclick="toggleSwitch(this)" class="relative inline-flex h-6 w-11 items-center rounded-full bg-blue-600 transition-colors focus:outline-none">
                                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6"></span>
                                        </button>
                                    </div>
                                    <button class="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                        <i class="fas fa-save mr-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>

                            <!-- Security Settings -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <i class="fas fa-shield-alt mr-2 text-green-600"></i>Security Settings
                                    </h3>
                                </div>
                                <div class="p-6 space-y-6">
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="text-sm font-medium text-gray-700">Two-Factor Authentication</p>
                                            <p class="text-xs text-gray-500">Add an extra layer of security</p>
                                        </div>
                                        <button onclick="toggleSwitch(this)" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none">
                                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1"></span>
                                        </button>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout</label>
                                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option>15 minutes</option>
                                            <option selected>30 minutes</option>
                                            <option>1 hour</option>
                                            <option>4 hours</option>
                                            <option>Never</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Password Policy</label>
                                        <div class="space-y-2">
                                            <div class="flex items-center">
                                                <input type="checkbox" checked class="mr-2 rounded">
                                                <span class="text-sm text-gray-600">Minimum 8 characters</span>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" checked class="mr-2 rounded">
                                                <span class="text-sm text-gray-600">Require uppercase letters</span>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" class="mr-2 rounded">
                                                <span class="text-sm text-gray-600">Require special characters</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                                        <i class="fas fa-shield-alt mr-2"></i>Update Security
                                    </button>
                                </div>
                            </div>

                            <!-- System Information -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 lg:col-span-2">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <i class="fas fa-info-circle mr-2 text-purple-600"></i>System Information
                                    </h3>
                                </div>
                                <div class="p-6">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                                            <i class="fas fa-server text-2xl text-blue-600 mb-2"></i>
                                            <p class="text-sm font-medium text-gray-700">GenieACS Version</p>
                                            <p class="text-lg font-bold text-gray-900">v1.2.8</p>
                                        </div>
                                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                                            <i class="fas fa-clock text-2xl text-green-600 mb-2"></i>
                                            <p class="text-sm font-medium text-gray-700">Uptime</p>
                                            <p class="text-lg font-bold text-gray-900">15d 4h 23m</p>
                                        </div>
                                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                                            <i class="fas fa-memory text-2xl text-purple-600 mb-2"></i>
                                            <p class="text-sm font-medium text-gray-700">Memory Usage</p>
                                            <p class="text-lg font-bold text-gray-900">2.1 GB / 8 GB</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        let currentPage = 'dashboard';

        function showPage(page) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(el => {
                el.classList.add('hidden');
            });

            // Show selected page
            document.getElementById(page + '-page').classList.remove('hidden');

            // Update navigation
            document.querySelectorAll('.nav-item').forEach(el => {
                el.classList.remove('bg-blue-600', 'text-white');
                el.classList.add('text-gray-700', 'hover:bg-gray-100');
            });

            document.getElementById('nav-' + page).classList.remove('text-gray-700', 'hover:bg-gray-100');
            document.getElementById('nav-' + page).classList.add('bg-blue-600', 'text-white');

            // Update page title
            document.getElementById('page-title').textContent = page.charAt(0).toUpperCase() + page.slice(1);

            currentPage = page;

            // Close mobile sidebar
            closeSidebar();
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-overlay');

            if (sidebar.classList.contains('-translate-x-full')) {
                sidebar.classList.remove('-translate-x-full');
                sidebar.classList.add('translate-x-0');
                overlay.classList.remove('hidden');
            } else {
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
                overlay.classList.add('hidden');
            }
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobile-overlay');

            if (window.innerWidth < 1024) {
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
                overlay.classList.add('hidden');
            }
        }

        function toggleSwitch(button) {
            const span = button.querySelector('span');

            if (button.classList.contains('bg-blue-600')) {
                button.classList.remove('bg-blue-600');
                button.classList.add('bg-gray-200');
                span.classList.remove('translate-x-6');
                span.classList.add('translate-x-1');
            } else {
                button.classList.add('bg-blue-600');
                button.classList.remove('bg-gray-200');
                span.classList.add('translate-x-6');
                span.classList.remove('translate-x-1');
            }
        }

        // Close sidebar when clicking overlay
        document.getElementById('mobile-overlay').addEventListener('click', closeSidebar);

        // Close sidebar on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) {
                closeSidebar();
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            showPage('dashboard');
        });
    </script>
</body>
</html>
