<?php
// Ambil URL API dari config.php
require_once 'config.php';

$baseUrl = GENIEACS_URL;

// Optimasi: hanya ambil field yang dibutuhkan
$projection = [
    '_id',
    '_deviceId._ProductClass',
    'VirtualParameters.pppoeUsername',
    'VirtualParameters.pppoeUsername2',
    'VirtualParameters.WANBRIDGE',
    'VirtualParameters.RXPower',
    'VirtualParameters.gettemp',
    'VirtualParameters.activedevices',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.1.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.2.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.3.SSID',
    'InternetGatewayDevice.LANDevice.1.WLANConfiguration.4.SSID',
    '_lastInform'
];

// Build URL dengan projection untuk mengurangi data
$apiUrl = $baseUrl . '?projection=' . urlencode(implode(',', $projection));
if (!$baseUrl) {
    http_response_code(500);
    echo json_encode(['error' => 'API URL not set in config.php']);
    exit;
}



// Ambil data dari API GenieACS dengan optimasi
$ch = curl_init($apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15); // Kurangi timeout
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Timeout koneksi
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // No redirect
curl_setopt($ch, CURLOPT_ENCODING, 'gzip'); // Kompresi
$result = curl_exec($ch);
if (curl_errno($ch)) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch API', 'detail' => curl_error($ch)]);
    exit;
}
curl_close($ch);

$data = json_decode($result, true);
if (!is_array($data)) {
    http_response_code(500);
    echo json_encode(['error' => 'Invalid API response']);
    exit;
}

// Filter dan susun data
$output = [];
foreach ($data as $item) {
    $vp = isset($item['VirtualParameters']) ? $item['VirtualParameters'] : [];
    $pppoe1 = isset($vp['pppoeUsername']) ? $vp['pppoeUsername']['_value'] : null;
    $pppoe2 = isset($vp['pppoeUsername2']) ? $vp['pppoeUsername2']['_value'] : null;
    $pppsecret = $pppoe1 ? $pppoe1 : $pppoe2;
    $wanbridge = isset($vp['WANBRIDGE']) ? $vp['WANBRIDGE']['_value'] : null;
    $rxpower = isset($vp['RXPower']) ? $vp['RXPower']['_value'] : null;
    $gettemp = isset($vp['gettemp']) ? $vp['gettemp']['_value'] : null;
    $activedevices = isset($vp['activedevices']) ? $vp['activedevices']['_value'] : null;
    $_lastInformUTC = isset($item['_lastInform']) ? $item['_lastInform'] : null;
    $typeont = isset($item['_deviceId']['_ProductClass']) ? $item['_deviceId']['_ProductClass'] : null;
    $deviceId = isset($item['_id']) ? $item['_id'] : null;

    // Ambil SSID dari WLANConfiguration 1-4
    $ssid1 = isset($item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['1']['SSID']['_value']) ? $item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['1']['SSID']['_value'] : null;
    $ssid2 = isset($item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['2']['SSID']['_value']) ? $item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['2']['SSID']['_value'] : null;
    $ssid3 = isset($item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['3']['SSID']['_value']) ? $item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['3']['SSID']['_value'] : null;
    $ssid4 = isset($item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['4']['SSID']['_value']) ? $item['InternetGatewayDevice']['LANDevice']['1']['WLANConfiguration']['4']['SSID']['_value'] : null;

    // Format _lastInform ke WIB (Asia/Jakarta)
    $_lastInformWIB = null;
    if ($_lastInformUTC) {
        try {
            $dt = new DateTime($_lastInformUTC, new DateTimeZone('UTC'));
            $dt->setTimezone(new DateTimeZone('Asia/Jakarta'));
            $_lastInformWIB = $dt->format('Y-m-d H:i:s'); // contoh: 2025-07-24 21:56:12
        } catch (Exception $e) {
            $_lastInformWIB = 'Invalid date';
        }
    }

    $output[] = [
        '_id' => $deviceId,
        'productclass' => $typeont,
        'pppoe' => $pppsecret,
        'wanbridge' => $wanbridge,
        'rxpower' => $rxpower,
        'temperature' => $gettemp,
        'activedevices' => $activedevices,
        'ssid1' => $ssid1,
        'ssid2' => $ssid2,
        'ssid3' => $ssid3,
        'ssid4' => $ssid4,
        '_lastInform' => $_lastInformWIB
    ];
}

// Tampilkan ke browser
header('Content-Type: application/json');
echo json_encode($output, JSON_PRETTY_PRINT);
