<!-- //settings -->
<div class="fade-in">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- General Settings -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-cog mr-2 text-blue-600"></i>General Settings
                </h3>
            </div>
            <div class="p-6 space-y-6">
                <form id="general-settings-form">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Server Name</label>
                        <input type="text" id="server-name" value="GenieACS Server" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Auto Refresh Interval</label>
                        <select id="refresh-interval" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="30">30 seconds</option>
                            <option value="60" selected>1 minute</option>
                            <option value="300">5 minutes</option>
                            <option value="600">10 minutes</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                        <select id="timezone" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="UTC">UTC</option>
                            <option value="Asia/Jakarta" selected>Asia/Jakarta</option>
                            <option value="America/New_York">America/New_York</option>
                            <option value="Europe/London">Europe/London</option>
                        </select>
                    </div>
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <p class="text-sm font-medium text-gray-700">Enable Notifications</p>
                            <p class="text-xs text-gray-500">Get notified about device status changes</p>
                        </div>
                        <button type="button" id="notifications-toggle" onclick="toggleNotifications()" class="relative inline-flex h-6 w-11 items-center rounded-full bg-blue-600 transition-colors focus:outline-none">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6"></span>
                        </button>
                    </div>
                    <button type="submit" class="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Security Settings -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-shield-alt mr-2 text-green-600"></i>Security Settings
                </h3>
            </div>
            <div class="p-6 space-y-6">
                <form id="security-settings-form">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <p class="text-sm font-medium text-gray-700">Two-Factor Authentication</p>
                            <p class="text-xs text-gray-500">Add an extra layer of security</p>
                        </div>
                        <button type="button" id="2fa-toggle" onclick="toggle2FA()" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1"></span>
                        </button>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout</label>
                        <select id="session-timeout" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="15">15 minutes</option>
                            <option value="30" selected>30 minutes</option>
                            <option value="60">1 hour</option>
                            <option value="240">4 hours</option>
                            <option value="0">Never</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password Policy</label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="min-length" checked class="mr-2 rounded">
                                <label for="min-length" class="text-sm text-gray-600">Minimum 8 characters</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="uppercase" checked class="mr-2 rounded">
                                <label for="uppercase" class="text-sm text-gray-600">Require uppercase letters</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="special-chars" class="mr-2 rounded">
                                <label for="special-chars" class="text-sm text-gray-600">Require special characters</label>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                        <i class="fas fa-shield-alt mr-2"></i>Update Security
                    </button>
                </form>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 lg:col-span-2">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-info-circle mr-2 text-purple-600"></i>System Information
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <i class="fas fa-server text-2xl text-blue-600 mb-2"></i>
                        <p class="text-sm font-medium text-gray-700">GenieACS Version</p>
                        <p class="text-lg font-bold text-gray-900" id="genieacs-version">v1.2.8</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <i class="fas fa-clock text-2xl text-green-600 mb-2"></i>
                        <p class="text-sm font-medium text-gray-700">Uptime</p>
                        <p class="text-lg font-bold text-gray-900" id="system-uptime">15d 4h 23m</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <i class="fas fa-memory text-2xl text-purple-600 mb-2"></i>
                        <p class="text-sm font-medium text-gray-700">Memory Usage</p>
                        <p class="text-lg font-bold text-gray-900" id="memory-usage">2.1 GB / 8 GB</p>
                    </div>
                </div>
                
                <!-- Additional System Info -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="p-4 bg-blue-50 rounded-lg">
                        <h4 class="font-medium text-blue-900 mb-2">Database Status</h4>
                        <p class="text-sm text-blue-700">Connected • Last backup: 2 hours ago</p>
                    </div>
                    <div class="p-4 bg-green-50 rounded-lg">
                        <h4 class="font-medium text-green-900 mb-2">API Status</h4>
                        <p class="text-sm text-green-700">Operational • Response time: 45ms</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
